unit DatabaseSearchForm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls,
  Vcl.ComCtrls, Vcl.Grids, Data.DB, FireDAC.Comp.Client, System.Generics.Collections, ExceptionLogger;

type
  TSearchResult = record
    TableName: string;
    FieldName: string;
    RowIndex: Integer;
    MatchText: string;
    FullRowData: string;
  end;

  TfrmDatabaseSearch = class(TForm)
    pnlMain: TPanel;
    gbSearchOptions: TGroupBox;
    lblSearchText: TLabel;
    edtSearchText: TEdit;
    btnSearch: TButton;
    chkCaseSensitive: TCheckBox;
    chkWholeWord: TCheckBox;
    chkRegex: TCheckBox;
    gbSearchScope: TGroupBox;
    rbCurrentTable: TRadioButton;
    rbAllTables: TRadioButton;
    rbSelectedTables: TRadioButton;
    lbTables: TListBox;
    btnSelectAll: TButton;
    btnSelectNone: TButton;
    pnlResults: TPanel;
    lblResults: TLabel;
    lvResults: TListView;
    pnlButtons: TPanel;
    btnClose: TButton;
    btnExport: TButton;
    progressBar: TProgressBar;
    lblStatus: TLabel;
    
    procedure FormCreate(Sender: TObject);
    procedure btnSearchClick(Sender: TObject);
    procedure btnCloseClick(Sender: TObject);
    procedure btnExportClick(Sender: TObject);
    procedure btnSelectAllClick(Sender: TObject);
    procedure btnSelectNoneClick(Sender: TObject);
    procedure rbSelectedTablesClick(Sender: TObject);
    procedure lvResultsDblClick(Sender: TObject);
    procedure edtSearchTextKeyPress(Sender: TObject; var Key: Char);
    
  private
    FConnection: TFDConnection;
    FCurrentTable: string;
    FSearchResults: TList<TSearchResult>;
    
    procedure LoadTableList;
    procedure SearchInTable(const TableName: string);
    procedure SearchInAllTables;
    procedure AddSearchResult(const TableName, FieldName: string; RowIndex: Integer; 
      const MatchText, FullRowData: string);
    procedure UpdateResultsDisplay;
    function MatchesSearchCriteria(const Text, SearchText: string): Boolean;
    procedure ExportResults;
    
  public
    property Connection: TFDConnection read FConnection write FConnection;
    property CurrentTable: string read FCurrentTable write FCurrentTable;
    
    class procedure ShowSearchDialog(AConnection: TFDConnection; const ACurrentTable: string = '');
  end;

implementation

uses
  System.RegularExpressions, System.StrUtils, DataExportForm;

{$R *.dfm}

class procedure TfrmDatabaseSearch.ShowSearchDialog(AConnection: TFDConnection; const ACurrentTable: string);
var
  frm: TfrmDatabaseSearch;
begin
  frm := TfrmDatabaseSearch.Create(nil);
  try
    frm.Connection := AConnection;
    frm.CurrentTable := ACurrentTable;
    frm.LoadTableList;
    frm.ShowModal;
  finally
    frm.Free;
  end;
end;

procedure TfrmDatabaseSearch.FormCreate(Sender: TObject);
begin
  FSearchResults := TList<TSearchResult>.Create;
  
  // 设置默认选项
  rbCurrentTable.Checked := True;
  chkCaseSensitive.Checked := False;
  chkWholeWord.Checked := False;
  chkRegex.Checked := False;
  
  // 设置结果列表
  lvResults.ViewStyle := vsReport;
  lvResults.Columns.Add.Caption := #$8868#$540D;        // '表名' 的Unicode编码
  lvResults.Columns.Add.Caption := #$5B57#$6BB5#$540D;  // '字段名' 的Unicode编码
  lvResults.Columns.Add.Caption := #$884C#$53F7;        // '行号' 的Unicode编码
  lvResults.Columns.Add.Caption := #$5339#$914D#$5185#$5BB9;  // '匹配内容' 的Unicode编码
  lvResults.Columns.Add.Caption := #$5B8C#$6574#$884C#$6570#$636E;  // '完整行数据' 的Unicode编码
  
  // 设置列宽
  lvResults.Columns[0].Width := 120;
  lvResults.Columns[1].Width := 100;
  lvResults.Columns[2].Width := 60;
  lvResults.Columns[3].Width := 150;
  lvResults.Columns[4].Width := 300;
  
  progressBar.Visible := False;
end;

procedure TfrmDatabaseSearch.LoadTableList;
var
  Query: TFDQuery;
  Index: Integer;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;
    
  lbTables.Items.Clear;
  
  Query := TFDQuery.Create(nil);
  try
    Query.Connection := FConnection;
    Query.SQL.Text := 'SELECT name FROM sqlite_master WHERE type=''table'' ORDER BY name';
    Query.Open;
    
    while not Query.Eof do
    begin
      lbTables.Items.Add(Query.FieldByName('name').AsString);
      Query.Next;
    end;
    
    // 如果有当前表，选中它
    if FCurrentTable <> '' then
    begin
      Index := lbTables.Items.IndexOf(FCurrentTable);
      if Index >= 0 then
        lbTables.ItemIndex := Index;
    end;
    
  finally
    Query.Free;
  end;
end;

procedure TfrmDatabaseSearch.rbSelectedTablesClick(Sender: TObject);
begin
  lbTables.Enabled := rbSelectedTables.Checked;
  btnSelectAll.Enabled := rbSelectedTables.Checked;
  btnSelectNone.Enabled := rbSelectedTables.Checked;
end;

procedure TfrmDatabaseSearch.btnSelectAllClick(Sender: TObject);
var
  I: Integer;
begin
  for I := 0 to lbTables.Items.Count - 1 do
    lbTables.Selected[I] := True;
end;

procedure TfrmDatabaseSearch.btnSelectNoneClick(Sender: TObject);
var
  I: Integer;
begin
  for I := 0 to lbTables.Items.Count - 1 do
    lbTables.Selected[I] := False;
end;

procedure TfrmDatabaseSearch.edtSearchTextKeyPress(Sender: TObject; var Key: Char);
begin
  if Key = #13 then // Enter键
  begin
    Key := #0;
    btnSearchClick(nil);
  end;
end;

function TfrmDatabaseSearch.MatchesSearchCriteria(const Text, SearchText: string): Boolean;
var
  CompareText, CompareSearch: string;
begin
  Result := False;
  
  if Trim(SearchText) = '' then
    Exit;
  
  if chkRegex.Checked then
  begin
    try
      Result := TRegEx.IsMatch(Text, SearchText);
    except
      Result := False; // 正则表达式错误
    end;
  end
  else
  begin
    if chkCaseSensitive.Checked then
    begin
      CompareText := Text;
      CompareSearch := SearchText;
    end
    else
    begin
      CompareText := LowerCase(Text);
      CompareSearch := LowerCase(SearchText);
    end;
    
    if chkWholeWord.Checked then
    begin
      // 简单的全词匹配
      Result := (CompareText = CompareSearch);
    end
    else
    begin
      Result := Pos(CompareSearch, CompareText) > 0;
    end;
  end;
end;

procedure TfrmDatabaseSearch.AddSearchResult(const TableName, FieldName: string; 
  RowIndex: Integer; const MatchText, FullRowData: string);
var
  SearchResult: TSearchResult;
begin
  SearchResult.TableName := TableName;
  SearchResult.FieldName := FieldName;
  SearchResult.RowIndex := RowIndex;
  SearchResult.MatchText := MatchText;
  SearchResult.FullRowData := FullRowData;
  
  FSearchResults.Add(SearchResult);
end;

procedure TfrmDatabaseSearch.SearchInTable(const TableName: string);
var
  Query: TFDQuery;
  I, RowIndex: Integer;
  FieldValue, FullRowData: string;
  SearchText: string;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;
    
  SearchText := Trim(edtSearchText.Text);
  if SearchText = '' then
    Exit;
    
  Query := TFDQuery.Create(nil);
  try
    Query.Connection := FConnection;
    Query.SQL.Text := 'SELECT * FROM ' + TableName;
    Query.Open;
    
    RowIndex := 0;
    while not Query.Eof do
    begin
      // 构建完整行数据
      FullRowData := '';
      for I := 0 to Query.FieldCount - 1 do
      begin
        if I > 0 then
          FullRowData := FullRowData + ' | ';
        FullRowData := FullRowData + Query.Fields[I].AsString;
      end;
      
      // 搜索每个字段
      for I := 0 to Query.FieldCount - 1 do
      begin
        FieldValue := Query.Fields[I].AsString;
        if MatchesSearchCriteria(FieldValue, SearchText) then
        begin
          AddSearchResult(TableName, Query.Fields[I].FieldName, RowIndex, 
            FieldValue, FullRowData);
        end;
      end;
      
      Query.Next;
      Inc(RowIndex);
      
      // 更新进度
      if RowIndex mod 100 = 0 then
      begin
        Application.ProcessMessages;
        lblStatus.Caption := Format(#$6B63#$5728#$641C#$7D22#$8868 + ' %s' + #$FF0C#$5DF2#$5904#$7406 + ' %d ' + #$884C + '...', [TableName, RowIndex]);  // '正在搜索表 %s，已处理 %d 行...' 的Unicode编码
      end;
    end;
    
  finally
    Query.Free;
  end;
end;

procedure TfrmDatabaseSearch.SearchInAllTables;
var
  I: Integer;
  TableName: string;
  TotalTables, CurrentTable: Integer;
begin
  TotalTables := 0;
  CurrentTable := 0;
  
  // 计算要搜索的表数量
  if rbCurrentTable.Checked then
    TotalTables := 1
  else if rbAllTables.Checked then
    TotalTables := lbTables.Items.Count
  else if rbSelectedTables.Checked then
  begin
    for I := 0 to lbTables.Items.Count - 1 do
      if lbTables.Selected[I] then
        Inc(TotalTables);
  end;
  
  if TotalTables = 0 then
  begin
    LogAndShowWarning('DatabaseSearchForm', #$8BF7#$9009#$62E9#$8981#$641C#$7D22#$7684#$8868, #$7528#$6237#$5C1D#$8BD5#$641C#$7D22#$4F46#$672A#$9009#$62E9#$8868);  // '请选择要搜索的表', '用户尝试搜索但未选择表' 的Unicode编码
    Exit;
  end;
  
  progressBar.Max := TotalTables;
  progressBar.Position := 0;
  progressBar.Visible := True;
  
  try
    if rbCurrentTable.Checked then
    begin
      if FCurrentTable <> '' then
      begin
        lblStatus.Caption := '正在搜索当前表: ' + FCurrentTable;
        SearchInTable(FCurrentTable);
        progressBar.Position := 1;
      end;
    end
    else if rbAllTables.Checked then
    begin
      for I := 0 to lbTables.Items.Count - 1 do
      begin
        TableName := lbTables.Items[I];
        lblStatus.Caption := Format(string('正在搜索表 %s (%d/%d)'), [TableName, I + 1, TotalTables]);
        SearchInTable(TableName);
        progressBar.Position := I + 1;
        Application.ProcessMessages;
      end;
    end
    else if rbSelectedTables.Checked then
    begin
      for I := 0 to lbTables.Items.Count - 1 do
      begin
        if lbTables.Selected[I] then
        begin
          TableName := lbTables.Items[I];
          Inc(CurrentTable);
          lblStatus.Caption := Format(string('正在搜索表 %s (%d/%d)'), [TableName, CurrentTable, TotalTables]);
          SearchInTable(TableName);
          progressBar.Position := CurrentTable;
          Application.ProcessMessages;
        end;
      end;
    end;
    
  finally
    progressBar.Visible := False;
    lblStatus.Caption := Format(string('搜索完成，找到 %d 个匹配项'), [FSearchResults.Count]);
  end;
end;

procedure TfrmDatabaseSearch.UpdateResultsDisplay;
var
  I: Integer;
  ListItem: TListItem;
  SearchResult: TSearchResult;
begin
  lvResults.Items.Clear;
  
  for I := 0 to FSearchResults.Count - 1 do
  begin
    SearchResult := FSearchResults[I];
    ListItem := lvResults.Items.Add;
    ListItem.Caption := SearchResult.TableName;
    ListItem.SubItems.Add(SearchResult.FieldName);
    ListItem.SubItems.Add(IntToStr(SearchResult.RowIndex + 1)); // 显示时从1开始
    ListItem.SubItems.Add(SearchResult.MatchText);
    ListItem.SubItems.Add(SearchResult.FullRowData);
    ListItem.Data := Pointer(I); // 存储索引
  end;
  
  lblResults.Caption := Format(string('搜索结果 (%d 项)'), [FSearchResults.Count]);
end;

procedure TfrmDatabaseSearch.btnSearchClick(Sender: TObject);
begin
  if Trim(edtSearchText.Text) = '' then
  begin
    LogAndShowWarning('DatabaseSearchForm', string('请输入搜索关键字'), string('用户尝试搜索但未输入关键字'));
    edtSearchText.SetFocus;
    Exit;
  end;

  if not Assigned(FConnection) or not FConnection.Connected then
  begin
    LogAndShowError('DatabaseSearchForm', string('数据库连接无效'), nil, string('用户尝试搜索'), True);
    Exit;
  end;
  
  // 清空之前的结果
  FSearchResults.Clear;
  lvResults.Items.Clear;
  
  btnSearch.Enabled := False;
  try
    SearchInAllTables;
    UpdateResultsDisplay;
  finally
    btnSearch.Enabled := True;
  end;
end;

procedure TfrmDatabaseSearch.lvResultsDblClick(Sender: TObject);
var
  SelectedItem: TListItem;
  SearchResult: TSearchResult;
  Msg: string;
  Index: Integer;
begin
  SelectedItem := lvResults.Selected;
  if not Assigned(SelectedItem) then
    Exit;

  Index := Integer(SelectedItem.Data);
  if (Index >= 0) and (Index < FSearchResults.Count) then
  begin
    SearchResult := FSearchResults[Index];
    Msg := Format('表名: %s'#13#10'字段: %s'#13#10'行号: %d'#13#10'匹配内容: %s'#13#10#13#10'完整行数据:'#13#10'%s',
      [SearchResult.TableName, SearchResult.FieldName, SearchResult.RowIndex + 1,
       SearchResult.MatchText, SearchResult.FullRowData]);
    LogInfo('DatabaseSearchForm', Msg, '用户查看搜索结果详情');
  end;
end;

procedure TfrmDatabaseSearch.ExportResults;
var
  MemTable: TFDMemTable;
  I: Integer;
  SearchResult: TSearchResult;
begin
  if FSearchResults.Count = 0 then
  begin
    LogAndShowWarning('DatabaseSearchForm', '没有搜索结果可导出', '用户尝试导出但无搜索结果');
    Exit;
  end;
  
  MemTable := TFDMemTable.Create(nil);
  try
    // 创建字段
    MemTable.FieldDefs.Add('表名', ftString, 100);
    MemTable.FieldDefs.Add('字段名', ftString, 100);
    MemTable.FieldDefs.Add('行号', ftInteger);
    MemTable.FieldDefs.Add('匹配内容', ftString, 500);
    MemTable.FieldDefs.Add('完整行数据', ftString, 2000);
    
    MemTable.CreateDataSet;
    MemTable.Active := True;
    
    // 添加数据
    for I := 0 to FSearchResults.Count - 1 do
    begin
      SearchResult := FSearchResults[I];
      MemTable.Append;
      MemTable.FieldByName('表名').AsString := SearchResult.TableName;
      MemTable.FieldByName('字段名').AsString := SearchResult.FieldName;
      MemTable.FieldByName('行号').AsInteger := SearchResult.RowIndex + 1;
      MemTable.FieldByName('匹配内容').AsString := SearchResult.MatchText;
      MemTable.FieldByName('完整行数据').AsString := SearchResult.FullRowData;
      MemTable.Post;
    end;
    
    // 调用导出对话框
    TfrmDataExport.ShowExportDialog(MemTable, '搜索结果');
    
  finally
    MemTable.Free;
  end;
end;

procedure TfrmDatabaseSearch.btnExportClick(Sender: TObject);
begin
  ExportResults;
end;

procedure TfrmDatabaseSearch.btnCloseClick(Sender: TObject);
begin
  Close;
end;

end.
