object frmExceptionLog: TfrmExceptionLog
  Left = 0
  Top = 0
  Caption = #24322#24120#26085#24535#31649#29702#22120
  ClientHeight = 600
  ClientWidth = 800
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poScreenCenter
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object pnlMain: TPanel
    Left = 0
    Top = 0
    Width = 800
    Height = 600
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    object pnlTop: TPanel
      Left = 0
      Top = 0
      Width = 800
      Height = 50
      Align = alTop
      BevelOuter = bvLowered
      TabOrder = 0
      object lblLevel: TLabel
        Left = 16
        Top = 16
        Width = 60
        Height = 13
        Caption = #38169#35823#31561#32423':'
      end
      object lblRetention: TLabel
        Left = 200
        Top = 16
        Width = 48
        Height = 13
        Caption = #20445#30041#35774#32622':'
      end
      object cbLevel: TComboBox
        Left = 82
        Top = 13
        Width = 100
        Height = 21
        Style = csDropDownList
        TabOrder = 0
        OnChange = cbLevelChange
      end
      object cbRetention: TComboBox
        Left = 254
        Top = 13
        Width = 100
        Height = 21
        Style = csDropDownList
        TabOrder = 1
        OnChange = cbRetentionChange
      end
      object btnClear: TButton
        Left = 380
        Top = 12
        Width = 75
        Height = 25
        Caption = #28165#38500#20840#37096
        TabOrder = 2
        OnClick = btnClearClick
      end
      object btnExport: TButton
        Left = 470
        Top = 12
        Width = 75
        Height = 25
        Caption = #23548#20986#26085#24535
        TabOrder = 3
        OnClick = btnExportClick
      end
    end
    object pnlCenter: TPanel
      Left = 0
      Top = 50
      Width = 800
      Height = 500
      Align = alClient
      BevelOuter = bvNone
      TabOrder = 1
      object splitter: TSplitter
        Left = 0
        Top = 300
        Width = 800
        Height = 5
        Cursor = crVSplit
        Align = alTop
        ExplicitTop = 250
        ExplicitWidth = 185
      end
      object lvLogs: TListView
        Left = 0
        Top = 0
        Width = 800
        Height = 300
        Align = alTop
        Columns = <
          item
            Caption = #26102#38388
            Width = 140
          end
          item
            Caption = #31561#32423
            Width = 60
          end
          item
            Caption = #27169#22359
            Width = 120
          end
          item
            Caption = #28040#24687
            Width = 300
          end
          item
            Caption = #29366#24577
            Width = 60
          end>
        GridLines = True
        RowSelect = True
        TabOrder = 0
        ViewStyle = vsReport
        OnDblClick = lvLogsDblClick
        OnSelectItem = lvLogsSelectItem
      end
      object pnlDetails: TPanel
        Left = 0
        Top = 305
        Width = 800
        Height = 195
        Align = alClient
        BevelOuter = bvLowered
        TabOrder = 1
        object lblDetails: TLabel
          Left = 8
          Top = 8
          Width = 60
          Height = 13
          Caption = #35814#32454#20449#24687':'
        end
        object memoDetails: TMemo
          Left = 8
          Top = 27
          Width = 784
          Height = 160
          Anchors = [akLeft, akTop, akRight, akBottom]
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          ReadOnly = True
          ScrollBars = ssVertical
          TabOrder = 0
        end
      end
    end
    object pnlBottom: TPanel
      Left = 0
      Top = 550
      Width = 800
      Height = 50
      Align = alBottom
      BevelOuter = bvLowered
      TabOrder = 2
      object btnClose: TButton
        Left = 720
        Top = 12
        Width = 75
        Height = 25
        Anchors = [akTop, akRight]
        Caption = #20851#38381
        Default = True
        TabOrder = 0
        OnClick = btnCloseClick
      end
      object btnMarkAllRead: TButton
        Left = 620
        Top = 12
        Width = 90
        Height = 25
        Anchors = [akTop, akRight]
        Caption = #26631#35760#20840#37096#24050#35835
        TabOrder = 1
        OnClick = btnMarkAllReadClick
      end
    end
  end
  object saveDialog: TSaveDialog
    DefaultExt = 'log'
    Filter = #26085#24535#25991#20214' (*.log)|*.log|'#25991#26412#25991#20214' (*.txt)|*.txt|'#25152#26377#25991#20214' (*.*)|*.*'
    Title = #23548#20986#24322#24120#26085#24535
    Left = 400
    Top = 300
  end
end
