unit StringUtils;

interface

uses
  System.SysUtils;

// 中文字符串安全转换函数
function S(const AText: string): string; inline;
function SafeString(const AText: string): string; inline;

// 常用中文字符串常量
const
  // 通用文本
  STR_OK = '确定';
  STR_CANCEL = '取消';
  STR_YES = '是';
  STR_NO = '否';
  STR_CLOSE = '关闭';
  STR_SAVE = '保存';
  STR_OPEN = '打开';
  STR_BROWSE = '浏览...';
  
  // 异常日志相关
  STR_EXCEPTION_LOG_MANAGER = '异常日志管理器';
  STR_ERROR_LEVEL = '错误等级：';
  STR_RETENTION_SETTING = '保留设置：';
  STR_DETAILS = '详细信息：';
  STR_CLEAR_ALL = '清除全部';
  STR_EXPORT_LOG = '导出日志';
  STR_MARK_ALL_READ = '标记全部已读';
  
  // 列标题
  STR_TIME = '时间';
  STR_LEVEL = '等级';
  STR_MODULE = '模块';
  STR_MESSAGE = '消息';
  STR_STATUS = '状态';
  
  // 日志级别
  STR_LEVEL_ALL = '全部等级';
  STR_LEVEL_INFO = '信息';
  STR_LEVEL_WARNING = '警告';
  STR_LEVEL_ERROR = '错误';
  STR_LEVEL_CRITICAL = '严重';
  STR_LEVEL_UNKNOWN = '未知';
  
  // 保留设置
  STR_RETENTION_FOREVER = '永久保留';
  STR_RETENTION_7DAYS = '保留7天';
  STR_RETENTION_30DAYS = '保留30天';
  STR_RETENTION_90DAYS = '保留90天';
  STR_RETENTION_365DAYS = '保留365天';
  
  // 状态
  STR_STATUS_READ = '已读';
  STR_STATUS_UNREAD = '未读';
  
  // 文件类型
  STR_FILTER_LOG = '日志文件 (*.log)|*.log|文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*';
  STR_FILTER_CSV = 'CSV文件 (*.csv)|*.csv|文本文件 (*.txt)|*.txt|Excel文件 (*.xls)|*.xls|所有文件 (*.*)|*.*';
  
  // 消息
  STR_CONFIRM_CLEAR_ALL = '确定要清除所有异常日志记录吗？此操作不可撤销。';
  STR_EXPORT_SUCCESS = '日志导出成功！';
  STR_EXPORT_FAILED = '日志导出失败：';

implementation

function S(const AText: string): string;
begin
  Result := string(AText);
end;

function SafeString(const AText: string): string;
begin
  Result := string(AText);
end;

end.
