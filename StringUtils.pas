unit StringUtils;

interface

uses
  System.SysUtils;

// 中文字符串安全转换函数
function S(const AText: string): string; inline;
function SafeString(const AText: string): string; inline;

// 常用中文字符串常量
const
  // 通用文本
  STR_OK: string = '确定';
  STR_CANCEL: string = '取消';
  STR_YES: string = '是';
  STR_NO: string = '否';
  STR_CLOSE: string = '关闭';
  STR_SAVE: string = '保存';
  STR_OPEN: string = '打开';
  STR_BROWSE: string = '浏览...';

  // 异常日志相关
  STR_EXCEPTION_LOG_MANAGER: string = '异常日志管理器';
  STR_ERROR_LEVEL: string = '错误等级：';
  STR_RETENTION_SETTING: string = '保留设置：';
  STR_DETAILS: string = '详细信息：';
  STR_CLEAR_ALL: string = '清除全部';
  STR_EXPORT_LOG: string = '导出日志';
  STR_MARK_ALL_READ: string = '标记全部已读';
  
  // 列标题
  STR_TIME: string = '时间';
  STR_LEVEL: string = '等级';
  STR_MODULE: string = '模块';
  STR_MESSAGE: string = '消息';
  STR_STATUS: string = '状态';

  // 日志级别
  STR_LEVEL_ALL: string = '全部等级';
  STR_LEVEL_INFO: string = '信息';
  STR_LEVEL_WARNING: string = '警告';
  STR_LEVEL_ERROR: string = '错误';
  STR_LEVEL_CRITICAL: string = '严重';
  STR_LEVEL_UNKNOWN: string = '未知';

  // 保留设置
  STR_RETENTION_FOREVER: string = '永久保留';
  STR_RETENTION_7DAYS: string = '保留7天';
  STR_RETENTION_30DAYS: string = '保留30天';
  STR_RETENTION_90DAYS: string = '保留90天';
  STR_RETENTION_365DAYS: string = '保留365天';

  // 状态
  STR_STATUS_READ: string = '已读';
  STR_STATUS_UNREAD: string = '未读';
  
  // 文件类型
  STR_FILTER_LOG: string = '日志文件 (*.log)|*.log|文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*';
  STR_FILTER_CSV: string = 'CSV文件 (*.csv)|*.csv|文本文件 (*.txt)|*.txt|Excel文件 (*.xls)|*.xls|所有文件 (*.*)|*.*';

  // 消息
  STR_CONFIRM_CLEAR_ALL: string = '确定要清除所有异常日志记录吗？此操作不可撤销。';
  STR_EXPORT_SUCCESS: string = '日志导出成功！';
  STR_EXPORT_FAILED: string = '日志导出失败：';

implementation

function S(const AText: string): string;
begin
  Result := string(AText);
end;

function SafeString(const AText: string): string;
begin
  Result := string(AText);
end;

end.
