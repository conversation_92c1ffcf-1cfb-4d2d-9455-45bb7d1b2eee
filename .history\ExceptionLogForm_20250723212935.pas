unit ExceptionLogForm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls,
  Vcl.ComCtrls, Vcl.Grids, ExceptionLogger, System.Generics.Collections, System.StrUtils;

type
  TfrmExceptionLog = class(TForm)
    pnlMain: TPanel;
    pnlTop: TPanel;
    lblLevel: TLabel;
    cbLevel: TComboBox;
    lblRetention: TLabel;
    cbRetention: TComboBox;
    btnClear: TButton;
    btnExport: TButton;
    pnlCenter: TPanel;
    lvLogs: TListView;
    splitter: TSplitter;
    pnlDetails: TPanel;
    lblDetails: TLabel;
    memoDetails: TMemo;
    pnlBottom: TPanel;
    btnClose: TButton;
    btnMarkAllRead: TButton;
    saveDialog: TSaveDialog;
    
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure cbLevelChange(Sender: TObject);
    procedure cbRetentionChange(Sender: TObject);
    procedure btnClearClick(Sender: TObject);
    procedure btnExportClick(Sender: TObject);
    procedure btnCloseClick(Sender: TObject);
    procedure btnMarkAllReadClick(Sender: TObject);
    procedure lvLogsSelectItem(Sender: TObject; Item: TListItem; Selected: Boolean);
    procedure lvLogsDblClick(Sender: TObject);
    
  private
    FCurrentFilter: TLogLevel;
    FShowAllLevels: Boolean;
    
    procedure InitializeControls;
    procedure LoadLogRecords;
    procedure FilterRecords;
    procedure ShowRecordDetails(const Rec: TExceptionRecord);
    procedure UpdateRetentionSetting;
    function GetLogLevelText(Level: TLogLevel): string;
    function GetLogLevelColor(Level: TLogLevel): TColor;
    
  public
    class procedure ShowLogDialog;
  end;

implementation

{$R *.dfm}

uses
  System.IniFiles;

class procedure TfrmExceptionLog.ShowLogDialog;
var
  frm: TfrmExceptionLog;
begin
  frm := TfrmExceptionLog.Create(nil);
  try
    frm.ShowModal;
  finally
    frm.Free;
  end;
end;

procedure TfrmExceptionLog.FormCreate(Sender: TObject);
begin
  InitializeControls;
end;

procedure TfrmExceptionLog.FormShow(Sender: TObject);
begin
  // 确保字体设置正确
  Font.Name := 'Microsoft YaHei UI';
  Font.Charset := DEFAULT_CHARSET;
  Font.Size := 9;

  // 重新设置所有控件的字体和文本
  lblLevel.Font.Assign(Font);
  lblLevel.Caption := #$9519#$8BEF#$7B49#$7EA7#$FF1A;  // '错误等级:' 的Unicode编码

  lblRetention.Font.Assign(Font);
  lblRetention.Caption := #$4FDD#$7559#$8BBE#$7F6E#$FF1A;  // '保留设置:' 的Unicode编码

  lblDetails.Font.Assign(Font);
  lblDetails.Caption := #$8BE6#$7EC6#$4FE1#$606F#$FF1A;  // '详细信息:' 的Unicode编码

  btnClear.Caption := #$6E05#$9664#$5168#$90E8;  // '清除全部' 的Unicode编码
  btnExport.Caption := #$5BFC#$51FA#$65E5#$5FD7;  // '导出日志' 的Unicode编码
  btnClose.Caption := #$5173#$95ED;  // '关闭' 的Unicode编码
  btnMarkAllRead.Caption := #$6807#$8BB0#$5168#$90E8#$5DF2#$8BFB;  // '标记全部已读' 的Unicode编码

  // 重新设置列标题
  if lvLogs.Columns.Count >= 5 then
  begin
    lvLogs.Columns[0].Caption := #$65F6#$95F4;  // '时间' 的Unicode编码
    lvLogs.Columns[1].Caption := #$7B49#$7EA7;  // '等级' 的Unicode编码
    lvLogs.Columns[2].Caption := #$6A21#$5757;  // '模块' 的Unicode编码
    lvLogs.Columns[3].Caption := #$6D88#$606F;  // '消息' 的Unicode编码
    lvLogs.Columns[4].Caption := #$72B6#$6001;  // '状态' 的Unicode编码
  end;

  LoadLogRecords;

  // 标记所有记录为已读
  TExceptionLogger.Instance.MarkAllAsRead;
end;

procedure TfrmExceptionLog.InitializeControls;
begin
  // 设置窗体属性
  Caption := #$5F02#$5E38#$65E5#$5FD7#$7BA1#$7406#$5668;  // '异常日志管理器' 的Unicode编码
  Position := poScreenCenter;
  Width := 800;
  Height := 600;

  // 设置字体以支持中文显示
  Font.Name := 'Microsoft YaHei UI';
  Font.Charset := DEFAULT_CHARSET;
  Font.Size := 9;

  // 设置所有控件的字体和文本
  lblLevel.Font.Assign(Font);
  lblLevel.Caption := #$9519#$8BEF#$7B49#$7EA7#$FF1A;  // '错误等级:' 的Unicode编码

  lblRetention.Font.Assign(Font);
  lblRetention.Caption := #$4FDD#$7559#$8BBE#$7F6E#$FF1A;  // '保留设置:' 的Unicode编码

  lblDetails.Font.Assign(Font);
  lblDetails.Caption := #$8BE6#$7EC6#$4FE1#$606F#$FF1A;  // '详细信息:' 的Unicode编码

  cbLevel.Font.Assign(Font);
  cbRetention.Font.Assign(Font);

  btnClear.Font.Assign(Font);
  btnClear.Caption := #$6E05#$9664#$5168#$90E8;  // '清除全部' 的Unicode编码

  btnExport.Font.Assign(Font);
  btnExport.Caption := #$5BFC#$51FA#$65E5#$5FD7;  // '导出日志' 的Unicode编码

  btnClose.Font.Assign(Font);
  btnClose.Caption := #$5173#$95ED;  // '关闭' 的Unicode编码

  btnMarkAllRead.Font.Assign(Font);
  btnMarkAllRead.Caption := #$6807#$8BB0#$5168#$90E8#$5DF2#$8BFB;  // '标记全部已读' 的Unicode编码

  lvLogs.Font.Assign(Font);

  // 设置保存对话框
  saveDialog.Title := '导出异常日志';
  saveDialog.Filter := '日志文件 (*.log)|*.log|文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*';
  
  // 初始化等级过滤器
  cbLevel.Items.Clear;
  cbLevel.Items.Add(#$5168#$90E8#$7B49#$7EA7);  // '全部等级' 的Unicode编码
  cbLevel.Items.Add(#$4FE1#$606F);              // '信息' 的Unicode编码
  cbLevel.Items.Add(#$8B66#$544A);              // '警告' 的Unicode编码
  cbLevel.Items.Add(#$9519#$8BEF);              // '错误' 的Unicode编码
  cbLevel.Items.Add(#$4E25#$91CD);              // '严重' 的Unicode编码
  cbLevel.ItemIndex := 0;
  FShowAllLevels := True;
  
  // 初始化保留天数设置
  cbRetention.Items.Clear;
  cbRetention.Items.Add(#$6C38#$4E45#$4FDD#$7559);      // '永久保留' 的Unicode编码
  cbRetention.Items.Add(#$4FDD#$7559#$0037#$5929);      // '保留7天' 的Unicode编码
  cbRetention.Items.Add(#$4FDD#$7559#$0033#$0030#$5929); // '保留30天' 的Unicode编码
  cbRetention.Items.Add(#$4FDD#$7559#$0039#$0030#$5929); // '保留90天' 的Unicode编码
  cbRetention.Items.Add(#$4FDD#$7559#$0033#$0036#$0035#$5929); // '保留365天' 的Unicode编码
  
  case TExceptionLogger.Instance.RetentionDays of
    -1: cbRetention.ItemIndex := 0;
    7: cbRetention.ItemIndex := 1;
    30: cbRetention.ItemIndex := 2;
    90: cbRetention.ItemIndex := 3;
    365: cbRetention.ItemIndex := 4;
  else
    cbRetention.ItemIndex := 0;
  end;
  
  // 设置ListView
  lvLogs.ViewStyle := vsReport;
  lvLogs.RowSelect := True;
  lvLogs.GridLines := True;
  // lvLogs.FullRowSelect := True; // 这个属性在某些版本中可能不存在
  
  // 添加列
  lvLogs.Columns.Clear;
  with lvLogs.Columns.Add do
  begin
    Caption := #$65F6#$95F4;  // '时间' 的Unicode编码
    Width := 140;
  end;
  with lvLogs.Columns.Add do
  begin
    Caption := #$7B49#$7EA7;  // '等级' 的Unicode编码
    Width := 60;
  end;
  with lvLogs.Columns.Add do
  begin
    Caption := #$6A21#$5757;  // '模块' 的Unicode编码
    Width := 120;
  end;
  with lvLogs.Columns.Add do
  begin
    Caption := #$6D88#$606F;  // '消息' 的Unicode编码
    Width := 300;
  end;
  with lvLogs.Columns.Add do
  begin
    Caption := #$72B6#$6001;  // '状态' 的Unicode编码
    Width := 60;
  end;
  
  // 设置详细信息区域
  lblDetails.Caption := '详细信息:';
  memoDetails.ReadOnly := True;
  memoDetails.ScrollBars := ssVertical;
  memoDetails.Font.Name := 'Microsoft YaHei UI';
  memoDetails.Font.Charset := DEFAULT_CHARSET;
  memoDetails.Font.Size := 9;
  
  // 设置按钮
  btnClear.Caption := '清除全部';
  btnExport.Caption := '导出日志';
  btnClose.Caption := '关闭';
  btnMarkAllRead.Caption := '标记全部已读';
  
  // 设置保存对话框
  saveDialog.Title := '导出异常日志';
  saveDialog.Filter := '日志文件 (*.log)|*.log|文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*';
  saveDialog.DefaultExt := 'log';
end;

procedure TfrmExceptionLog.LoadLogRecords;
var
  Records: TArray<TExceptionRecord>;
  Rec: TExceptionRecord;
  Item: TListItem;
  i: Integer;
begin
  lvLogs.Items.Clear;
  
  Records := TExceptionLogger.Instance.GetRecords;
  
  // 按时间倒序排列（最新的在前面）
  for i := High(Records) downto Low(Records) do
  begin
    Rec := Records[i];
    
    // 应用过滤器
    if not FShowAllLevels and (Rec.Level <> FCurrentFilter) then
      Continue;
      
    Item := lvLogs.Items.Add;
    Item.Caption := FormatDateTime('mm-dd hh:nn:ss', Rec.Timestamp);
    Item.SubItems.Add(GetLogLevelText(Rec.Level));
    Item.SubItems.Add(Rec.Module);
    Item.SubItems.Add(Rec.Message);
    Item.SubItems.Add(IfThen(Rec.IsRead, #$5DF2#$8BFB, #$672A#$8BFB));  // '已读', '未读' 的Unicode编码
    Item.Data := Pointer(Rec.ID);
    
    // 设置颜色
    if not Rec.IsRead then
      Item.ImageIndex := -1; // 可以设置未读图标
      
    case Rec.Level of
      llError, llCritical:
        begin
          Item.SubItems.Objects[0] := TObject(GetLogLevelColor(Rec.Level));
        end;
    end;
  end;
  
  // 选择第一项
  if lvLogs.Items.Count > 0 then
  begin
    lvLogs.Items[0].Selected := True;
    lvLogs.Items[0].Focused := True;
  end;
end;

procedure TfrmExceptionLog.FilterRecords;
begin
  LoadLogRecords;
end;

procedure TfrmExceptionLog.ShowRecordDetails(const Rec: TExceptionRecord);
var
  Details: TStringList;
begin
  Details := TStringList.Create;
  try
    Details.Add('=== ' + #$5F02#$5E38#$8BE6#$7EC6#$4FE1#$606F + ' ===');  // '异常详细信息' 的Unicode编码
    Details.Add('');
    Details.Add(#$65F6#$95F4 + ': ' + FormatDateTime('yyyy-mm-dd hh:nn:ss', Rec.Timestamp));  // '时间' 的Unicode编码
    Details.Add(#$7B49#$7EA7 + ': ' + GetLogLevelText(Rec.Level));  // '等级' 的Unicode编码
    Details.Add(#$6A21#$5757 + ': ' + Rec.Module);  // '模块' 的Unicode编码
    Details.Add(#$6D88#$606F + ': ' + Rec.Message);  // '消息' 的Unicode编码
    Details.Add(#$72B6#$6001 + ': ' + IfThen(Rec.IsRead, #$5DF2#$8BFB, #$672A#$8BFB));  // '状态', '已读', '未读' 的Unicode编码
    
    if Rec.UserAction <> '' then
    begin
      Details.Add('');
      Details.Add(#$7528#$6237#$64CD#$4F5C + ': ' + Rec.UserAction);  // '用户操作' 的Unicode编码
    end;

    if Rec.ExceptionClass <> '' then
    begin
      Details.Add('');
      Details.Add(#$5F02#$5E38#$7C7B#$578B + ': ' + Rec.ExceptionClass);  // '异常类型' 的Unicode编码
    end;

    if Rec.ErrorCode <> 0 then
    begin
      Details.Add(#$9519#$8BEF#$4EE3#$7801 + ': ' + IntToStr(Rec.ErrorCode));  // '错误代码' 的Unicode编码
    end;

    if Rec.StackTrace <> '' then
    begin
      Details.Add('');
      Details.Add(#$5806#$6808#$8DDF#$8E2A + ':');  // '堆栈跟踪' 的Unicode编码
      Details.Add(Rec.StackTrace);
    end;
    
    memoDetails.Lines.Assign(Details);
  finally
    Details.Free;
  end;
end;

function TfrmExceptionLog.GetLogLevelText(Level: TLogLevel): string;
begin
  case Level of
    llInfo: Result := #$4FE1#$606F;        // '信息' 的Unicode编码
    llWarning: Result := #$8B66#$544A;     // '警告' 的Unicode编码
    llError: Result := #$9519#$8BEF;       // '错误' 的Unicode编码
    llCritical: Result := #$4E25#$91CD;    // '严重' 的Unicode编码
  else
    Result := #$672A#$77E5;                // '未知' 的Unicode编码
  end;
end;

function TfrmExceptionLog.GetLogLevelColor(Level: TLogLevel): TColor;
begin
  case Level of
    llInfo: Result := clBlue;
    llWarning: Result := clOlive;
    llError: Result := clRed;
    llCritical: Result := clMaroon;
  else
    Result := clBlack;
  end;
end;

procedure TfrmExceptionLog.UpdateRetentionSetting;
var
  ConfigFile: TIniFile;
  Days: Integer;
begin
  case cbRetention.ItemIndex of
    0: Days := -1;  // 永久保留
    1: Days := 7;   // 7天
    2: Days := 30;  // 30天
    3: Days := 90;  // 90天
    4: Days := 365; // 365天
  else
    Days := -1;
  end;

  TExceptionLogger.Instance.RetentionDays := Days;

  // 保存到配置文件
  ConfigFile := TIniFile.Create(ChangeFileExt(Application.ExeName, '.ini'));
  try
    ConfigFile.WriteInteger('异常日志', '保留天数', Days);
  finally
    ConfigFile.Free;
  end;
end;

// 事件处理方法
procedure TfrmExceptionLog.cbLevelChange(Sender: TObject);
begin
  case cbLevel.ItemIndex of
    0: FShowAllLevels := True;
    1: begin FShowAllLevels := False; FCurrentFilter := llInfo; end;
    2: begin FShowAllLevels := False; FCurrentFilter := llWarning; end;
    3: begin FShowAllLevels := False; FCurrentFilter := llError; end;
    4: begin FShowAllLevels := False; FCurrentFilter := llCritical; end;
  end;

  FilterRecords;
end;

procedure TfrmExceptionLog.cbRetentionChange(Sender: TObject);
begin
  UpdateRetentionSetting;
end;

procedure TfrmExceptionLog.btnClearClick(Sender: TObject);
begin
  if MessageDlg('确定要清除所有异常日志记录吗？此操作不可撤销。',
    mtConfirmation, [mbYes, mbNo], 0) = mrYes then
  begin
    TExceptionLogger.Instance.ClearAllRecords;
    LoadLogRecords;
    memoDetails.Clear;
  end;
end;

procedure TfrmExceptionLog.btnExportClick(Sender: TObject);
begin
  saveDialog.FileName := 'NewDBTool_异常日志_' + FormatDateTime('yyyymmdd_hhnnss', Now);

  if saveDialog.Execute then
  begin
    try
      TExceptionLogger.Instance.ExportToFile(saveDialog.FileName);
      // 在状态栏或标题栏显示成功信息，避免递归调用日志系统
      Caption := '异常日志查看器 - 导出成功: ' + ExtractFileName(saveDialog.FileName);
    except
      on E: Exception do
        // 在状态栏显示错误信息，避免递归调用日志系统
        Caption := '异常日志查看器 - 导出失败: ' + E.Message;
    end;
  end;
end;

procedure TfrmExceptionLog.btnCloseClick(Sender: TObject);
begin
  Close;
end;

procedure TfrmExceptionLog.btnMarkAllReadClick(Sender: TObject);
begin
  TExceptionLogger.Instance.MarkAllAsRead;
  LoadLogRecords;
end;

procedure TfrmExceptionLog.lvLogsSelectItem(Sender: TObject; Item: TListItem; Selected: Boolean);
var
  Records: TArray<TExceptionRecord>;
  RecordID: Integer;
  Rec: TExceptionRecord;
  i: Integer;
begin
  if not Selected or not Assigned(Item) then
  begin
    memoDetails.Clear;
    Exit;
  end;

  RecordID := Integer(Item.Data);
  Records := TExceptionLogger.Instance.GetRecords;

  for i := 0 to High(Records) do
  begin
    if Records[i].ID = RecordID then
    begin
      Rec := Records[i];
      ShowRecordDetails(Rec);

      // 标记为已读
      if not Rec.IsRead then
      begin
        TExceptionLogger.Instance.MarkAsRead(RecordID);
        Item.SubItems[3] := #$5DF2#$8BFB; // 更新状态列，'已读' 的Unicode编码
      end;

      Break;
    end;
  end;
end;

procedure TfrmExceptionLog.lvLogsDblClick(Sender: TObject);
begin
  // 双击时可以执行特殊操作，比如复制到剪贴板
  if Assigned(lvLogs.Selected) then
  begin
    // 这里可以添加双击处理逻辑
  end;
end;

end.
