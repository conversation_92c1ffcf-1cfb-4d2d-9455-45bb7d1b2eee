﻿unit DatabaseHistory;

interface

uses
  System.SysUtils, System.Classes, System.IniFiles, System.Generics.Collections,
  DBConnection, DatabaseManager;

type
  TDatabaseHistory = class
  private
    FDatabaseList: TObjectList<TDatabaseInfo>;
    FConfigFileName: string;
    
  public
    constructor Create(const AConfigFileName: string);
    destructor Destroy; override;
    
    // ??????????????
    procedure LoadDatabaseList;
    procedure SaveDatabaseList;
    procedure AddDatabaseToHistoryList(const APath, AName: string; ADBType: DBConnection.TDBType);
    procedure DeleteDatabaseFromHistory(const APath: string);
    procedure DeleteDirectoryFromHistory(const ADirPath: string);
    
    // ????
    property DatabaseList: TObjectList<TDatabaseInfo> read FDatabaseList;
    property ConfigFileName: string read FConfigFileName write FConfigFileName;
  end;

implementation

constructor TDatabaseHistory.Create(const AConfigFileName: string);
begin
  inherited Create;
  FConfigFileName := AConfigFileName;
  FDatabaseList := TObjectList<TDatabaseInfo>.Create(True);
end;

destructor TDatabaseHistory.Destroy;
begin
  FDatabaseList.Free;
  inherited;
end;

procedure TDatabaseHistory.LoadDatabaseList;
var
  IniFile: TIniFile;
  Sections: TStringList;
  i: Integer;
  SectionName, Path, Name, DBTypeStr: string;
  DBType: DBConnection.TDBType;
begin
  FDatabaseList.Clear;
  
  if not FileExists(FConfigFileName) then
    Exit;

  IniFile := TIniFile.Create(FConfigFileName);
  Sections := TStringList.Create;
  try
    IniFile.ReadSections(Sections);
    
    for i := 0 to Sections.Count - 1 do
    begin
      SectionName := Sections[i];
      if SectionName.StartsWith('Database') then
      begin
        Path := IniFile.ReadString(SectionName, 'Path', '');
        Name := IniFile.ReadString(SectionName, 'Name', '');
        DBTypeStr := IniFile.ReadString(SectionName, 'Type', 'SQLite');
        
        // ?????????????????
        if SameText(DBTypeStr, 'SQLite') then
          DBType := dbtSQLite
        else if SameText(DBTypeStr, 'Access') then
          DBType := dbtAccess
        else if SameText(DBTypeStr, 'Paradox') then
          DBType := dbtParadox
        else if SameText(DBTypeStr, 'ParadoxBDE') then
          DBType := dbtParadoxBDE
        else if SameText(DBTypeStr, 'ParadoxDirect') then
          DBType := dbtParadoxDirect
        else if SameText(DBTypeStr, 'Excel') then
          DBType := dbtExcel
        else
          DBType := dbtSQLite; // ???
          
        if (Path <> '') and (Name <> '') then
          FDatabaseList.Add(TDatabaseInfo.Create(Path, Name, DBType));
      end;
    end;
  finally
    Sections.Free;
    IniFile.Free;
  end;
end;

procedure TDatabaseHistory.SaveDatabaseList;
var
  IniFile: TIniFile;
  i: Integer;
  SectionName, DBTypeStr: string;
  DBInfo: TDatabaseInfo;
begin
  IniFile := TIniFile.Create(FConfigFileName);
  try
    // ?????????????????
    var Sections := TStringList.Create;
    try
      IniFile.ReadSections(Sections);
      for i := 0 to Sections.Count - 1 do
      begin
        if Sections[i].StartsWith('Database') then
          IniFile.EraseSection(Sections[i]);
      end;
    finally
      Sections.Free;
    end;
    
    // ???浱???????б?
    for i := 0 to FDatabaseList.Count - 1 do
    begin
      DBInfo := FDatabaseList[i];
      SectionName := Format('Database%d', [i + 1]);
      
      // ??????????????????
      case DBInfo.DBType of
        dbtSQLite: DBTypeStr := 'SQLite';
        dbtAccess: DBTypeStr := 'Access';
        dbtParadox: DBTypeStr := 'Paradox';
        dbtParadoxBDE: DBTypeStr := 'ParadoxBDE';
        dbtParadoxDirect: DBTypeStr := 'ParadoxDirect';
        dbtExcel: DBTypeStr := 'Excel';
      else
        DBTypeStr := 'SQLite';
      end;
      
      IniFile.WriteString(SectionName, 'Path', DBInfo.Path);
      IniFile.WriteString(SectionName, 'Name', DBInfo.Name);
      IniFile.WriteString(SectionName, 'Type', DBTypeStr);
    end;
  finally
    IniFile.Free;
  end;
end;

procedure TDatabaseHistory.AddDatabaseToHistoryList(const APath, AName: string; ADBType: DBConnection.TDBType);
var
  i: Integer;
  DBInfo: TDatabaseInfo;
  Found: Boolean;
begin
  // ???????????
  Found := False;
  for i := 0 to FDatabaseList.Count - 1 do
  begin
    if SameText(FDatabaseList[i].Path, APath) then
    begin
      Found := True;
      Break;
    end;
  end;
  
  if not Found then
  begin
    DBInfo := TDatabaseInfo.Create(APath, AName, ADBType);
    FDatabaseList.Add(DBInfo);
    SaveDatabaseList;
  end;
end;

procedure TDatabaseHistory.DeleteDatabaseFromHistory(const APath: string);
var
  i: Integer;
  DBInfo: TDatabaseInfo;
begin
  for i := FDatabaseList.Count - 1 downto 0 do
  begin
    DBInfo := FDatabaseList[i];
    if SameText(DBInfo.Path, APath) then
    begin
      FDatabaseList.Delete(i);
      Break;
    end;
  end;
  SaveDatabaseList;
end;

procedure TDatabaseHistory.DeleteDirectoryFromHistory(const ADirPath: string);
var
  i: Integer;
  DBInfo: TDatabaseInfo;
  DeletedCount: Integer;
begin
  DeletedCount := 0;
  
  for i := FDatabaseList.Count - 1 downto 0 do
  begin
    DBInfo := FDatabaseList[i];
    if SameText(ExtractFileDir(DBInfo.Path), ADirPath) then
    begin
      FDatabaseList.Delete(i);
      Inc(DeletedCount);
    end;
  end;
  
  SaveDatabaseList;
end;

end.
