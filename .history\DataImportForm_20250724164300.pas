unit DataImportForm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls,
  Vcl.ComCtrls, Vcl.Grids, Data.DB, FireDAC.Comp.Client, FireDAC.Comp.DataSet,
  System.StrUtils, ExceptionLogger;

type
  TfrmDataImport = class(TForm)
    pnlMain: TPanel;
    gbFileOptions: TGroupBox;
    lblFileName: TLabel;
    edtFileName: TEdit;
    btnBrowse: TButton;
    gbDelimiter: TGroupBox;
    rbComma: TRadioButton;
    rbSemicolon: TRadioButton;
    rbTab: TRadioButton;
    rbCustom: TRadioButton;
    edtCustomDelimiter: TEdit;
    btnAutoDetect: TButton;
    gbImportOptions: TGroupBox;
    chkFirstRowHeaders: TCheckBox;
    chkSkipEmptyLines: TCheckBox;
    chkTrimSpaces: TCheckBox;
    lblEncoding: TLabel;
    cbEncoding: TComboBox;
    pnlPreview: TPanel;
    lblPreview: TLabel;
    sgPreview: TStringGrid;
    pnlButtons: TPanel;
    btnPreview: TButton;
    btnOK: TButton;
    btnCancel: TButton;
    openDialog: TOpenDialog;
    lblStatus: TLabel;
    
    procedure FormCreate(Sender: TObject);
    procedure btnBrowseClick(Sender: TObject);
    procedure btnAutoDetectClick(Sender: TObject);
    procedure btnPreviewClick(Sender: TObject);
    procedure btnOKClick(Sender: TObject);
    procedure btnCancelClick(Sender: TObject);
    procedure rbCustomClick(Sender: TObject);
    procedure edtFileNameChange(Sender: TObject);
    
  private
    FTargetDataSet: TDataSet;
    FTableName: string;
    FPreviewData: TStringList;
    
    function GetDelimiter: string;
    function DetectDelimiter(const SampleText: string): string;
    procedure LoadPreview;
    function ImportData: Boolean;
    procedure ClearPreview;
    function ParseCSVLine(const Line: string; Delimiter: string): TStringList;
    
  public
    property TargetDataSet: TDataSet read FTargetDataSet write FTargetDataSet;
    property TableName: string read FTableName write FTableName;
    
    class function ShowImportDialog(ATargetDataSet: TDataSet; const ATableName: string): Boolean;
  end;

implementation

uses
  UnifiedExcelProcessor_POI, System.IOUtils, System.Math;

{$R *.dfm}

class function TfrmDataImport.ShowImportDialog(ATargetDataSet: TDataSet; const ATableName: string): Boolean;
var
  frm: TfrmDataImport;
begin
  Result := False;
  frm := TfrmDataImport.Create(nil);
  try
    frm.TargetDataSet := ATargetDataSet;
    frm.TableName := ATableName;
    Result := (frm.ShowModal = mrOK);
  finally
    frm.Free;
  end;
end;

procedure TfrmDataImport.FormCreate(Sender: TObject);
begin
  FPreviewData := TStringList.Create;

  // 设置字体以支持中文显示
  Font.Name := 'Microsoft YaHei UI';
  Font.Charset := DEFAULT_CHARSET;
  Font.Size := 9;

  // 设置窗体属性
  Caption := '数据导入';

  // 设置所有控件的中文文本
  gbFileOptions.Caption := '文件选项';
  lblFileName.Caption := string('文件名:');
  lblEncoding.Caption := string('编码:');
  btnBrowse.Caption := string('浏览...');

  gbDelimiter.Caption := string('分隔符设置');
  rbComma.Caption := string('逗号 (,)');
  rbSemicolon.Caption := string('分号 (;)');
  rbTab.Caption := string('制表符 (Tab)');
  rbCustom.Caption := string('自定义:');
  btnAutoDetect.Caption := string('自动检测');

  gbImportOptions.Caption := string('导入选项');
  chkFirstRowHeaders.Caption := string('首行为表头');
  chkSkipEmptyLines.Caption := string('跳过空行');
  chkTrimSpaces.Caption := string('去除空格');

  lblPreview.Caption := '数据预览:';
  lblStatus.Caption := '准备预览数据';

  btnPreview.Caption := '预览';
  btnOK.Caption := '导入';
  btnCancel.Caption := '取消';

  // 设置默认选项
  rbComma.Checked := True;
  chkFirstRowHeaders.Checked := True;
  chkSkipEmptyLines.Checked := True;
  chkTrimSpaces.Checked := True;

  // 设置编码选项
  cbEncoding.Items.Clear;
  cbEncoding.Items.Add(string('自动检测'));
  cbEncoding.Items.Add('UTF-8');
  cbEncoding.Items.Add('ANSI');
  cbEncoding.Items.Add('Unicode');
  cbEncoding.ItemIndex := 0;

  // 设置打开对话框
  openDialog.Filter := string('CSV文件 (*.csv)|*.csv|文本文件 (*.txt)|*.txt|Excel文件 (*.xls)|*.xls|所有文件 (*.*)|*.*');
  openDialog.DefaultExt := 'csv';

  // 初始化预览表格
  sgPreview.Font.Assign(Font);
  sgPreview.ColCount := 1;
  sgPreview.RowCount := 1;
  sgPreview.Cells[0, 0] := '请选择数据文件进行预览';

  ClearPreview;
end;

procedure TfrmDataImport.btnBrowseClick(Sender: TObject);
begin
  if openDialog.Execute then
  begin
    edtFileName.Text := openDialog.FileName;
    ClearPreview;
  end;
end;

procedure TfrmDataImport.edtFileNameChange(Sender: TObject);
begin
  ClearPreview;
end;

procedure TfrmDataImport.rbCustomClick(Sender: TObject);
begin
  edtCustomDelimiter.Enabled := rbCustom.Checked;
  if rbCustom.Checked then
    edtCustomDelimiter.SetFocus;
end;

function TfrmDataImport.GetDelimiter: string;
begin
  if rbComma.Checked then
    Result := ','
  else if rbSemicolon.Checked then
    Result := ';'
  else if rbTab.Checked then
    Result := #9
  else if rbCustom.Checked then
    Result := edtCustomDelimiter.Text
  else
    Result := ','; // 默认
end;

function TfrmDataImport.DetectDelimiter(const SampleText: string): string;
var
  CommaCount, SemicolonCount, TabCount: Integer;
  I: Integer;
begin
  CommaCount := 0;
  SemicolonCount := 0;
  TabCount := 0;
  
  for I := 1 to Length(SampleText) do
  begin
    case SampleText[I] of
      ',': Inc(CommaCount);
      ';': Inc(SemicolonCount);
      #9: Inc(TabCount);
    end;
  end;
  
  // 选择出现次数最多的分隔符
  if (CommaCount >= SemicolonCount) and (CommaCount >= TabCount) then
    Result := ','
  else if (SemicolonCount >= CommaCount) and (SemicolonCount >= TabCount) then
    Result := ';'
  else if TabCount > 0 then
    Result := #9
  else
    Result := ','; // 默认
end;

procedure TfrmDataImport.btnAutoDetectClick(Sender: TObject);
var
  FileContent: TStringList;
  SampleText: string;
  DetectedDelimiter: string;
  I: Integer;
  DelimiterName: string;
begin
  if not FileExists(edtFileName.Text) then
  begin
    LogAndShowWarning('DataImportForm', string('请先选择一个有效的文件'), string('用户尝试自动检测但文件不存在'));
    Exit;
  end;
  
  try
    FileContent := TStringList.Create;
    try
      FileContent.LoadFromFile(edtFileName.Text);
      
      if FileContent.Count > 0 then
      begin
        // 取前几行作为样本
        SampleText := '';
        for I := 0 to Min(4, FileContent.Count - 1) do
          SampleText := SampleText + FileContent[I] + #13#10;
        
        DetectedDelimiter := DetectDelimiter(SampleText);
        
        // 设置检测到的分隔符
        if DetectedDelimiter = ',' then
          rbComma.Checked := True
        else if DetectedDelimiter = ';' then
          rbSemicolon.Checked := True
        else if DetectedDelimiter = #9 then
          rbTab.Checked := True
        else
        begin
          rbCustom.Checked := True;
          edtCustomDelimiter.Text := DetectedDelimiter;
          edtCustomDelimiter.Enabled := True;
        end;
        
        if DetectedDelimiter = ',' then
          DelimiterName := '逗号 (,)'
        else if DetectedDelimiter = ';' then
          DelimiterName := '分号 (;)'
        else if DetectedDelimiter = #9 then
          DelimiterName := string('制表符 (Tab)')
        else
          DelimiterName := string('自定义: ') + DetectedDelimiter;
        LogInfo('DataImportForm', string('检测到分隔符: ') + DelimiterName, string('自动检测分隔符'));
      end
      else
        LogAndShowWarning('DataImportForm', string('文件为空'), string('自动检测分隔符但文件为空'));
        
    finally
      FileContent.Free;
    end;
    
  except
    on E: Exception do
      LogAndShowError('DataImportForm', string('自动检测失败'), E, string('自动检测分隔符'), True);
  end;
end;

procedure TfrmDataImport.ClearPreview;
begin
  sgPreview.ColCount := 1;
  sgPreview.RowCount := 1;
  sgPreview.Cells[0, 0] := '请选择数据文件进行预览';
  lblStatus.Caption := '准备预览数据';
end;

function TfrmDataImport.ParseCSVLine(const Line: string; Delimiter: string): TStringList;
var
  I: Integer;
  InQuotes: Boolean;
  CurrentField: string;
  Ch: Char;
begin
  Result := TStringList.Create;
  InQuotes := False;
  CurrentField := '';
  
  I := 1;
  while I <= Length(Line) do
  begin
    Ch := Line[I];

    if Ch = '"' then
    begin
      if InQuotes and (I < Length(Line)) and (Line[I + 1] = '"') then
      begin
        // 双引号转义
        CurrentField := CurrentField + '"';
        Inc(I); // 跳过下一个引号
      end
      else
        InQuotes := not InQuotes;
    end
    else if (Ch = Delimiter[1]) and not InQuotes then
    begin
      // 字段分隔符
      if chkTrimSpaces.Checked then
        CurrentField := Trim(CurrentField);
      Result.Add(CurrentField);
      CurrentField := '';
    end
    else
      CurrentField := CurrentField + Ch;

    Inc(I);
  end;
  
  // 添加最后一个字段
  if chkTrimSpaces.Checked then
    CurrentField := Trim(CurrentField);
  Result.Add(CurrentField);
end;

procedure TfrmDataImport.LoadPreview;
var
  FileContent: TStringList;
  I, J, K, MaxCols: Integer;
  Fields: TStringList;
  Delimiter: string;
  PreviewRows: Integer;
  MaxK: Integer;
begin
  if not FileExists(edtFileName.Text) then
  begin
    LogAndShowError('DataImportForm', '文件不存在: ' + edtFileName.Text, nil, '用户尝试加载预览', True);
    Exit;
  end;
  
  try
    FileContent := TStringList.Create;
    try
      // 根据编码加载文件
      case cbEncoding.ItemIndex of
        1: FileContent.LoadFromFile(edtFileName.Text, TEncoding.UTF8);
        2: FileContent.LoadFromFile(edtFileName.Text, TEncoding.Default);
        3: FileContent.LoadFromFile(edtFileName.Text, TEncoding.Unicode);
        else FileContent.LoadFromFile(edtFileName.Text); // 自动检测
      end;
      
      if FileContent.Count = 0 then
      begin
        LogAndShowWarning('DataImportForm', string('文件为空'), string('用户尝试加载预览但文件为空'));
        Exit;
      end;
      
      Delimiter := GetDelimiter;
      MaxCols := 0;
      if FileContent.Count < 20 then
        PreviewRows := FileContent.Count
      else
        PreviewRows := 20; // 最多预览20行
      
      // 先确定最大列数
      for I := 0 to PreviewRows - 1 do
      begin
        if chkSkipEmptyLines.Checked and (Trim(FileContent[I]) = '') then
          Continue;
          
        Fields := ParseCSVLine(FileContent[I], Delimiter);
        try
          if Fields.Count > MaxCols then
            MaxCols := Fields.Count;
        finally
          Fields.Free;
        end;
      end;
      
      if MaxCols = 0 then
      begin
        LogAndShowError('DataImportForm', '无法解析文件内容', nil, '用户尝试加载预览', True);
        Exit;
      end;
      
      // 设置表格大小
      sgPreview.ColCount := MaxCols;
      sgPreview.RowCount := PreviewRows + 1;
      
      // 清空表格
      for I := 0 to sgPreview.ColCount - 1 do
        for J := 0 to sgPreview.RowCount - 1 do
          sgPreview.Cells[I, J] := '';
      
      // 填充数据
      J := 0;
      for I := 0 to PreviewRows - 1 do
      begin
        if chkSkipEmptyLines.Checked and (Trim(FileContent[I]) = '') then
          Continue;
          
        Fields := ParseCSVLine(FileContent[I], Delimiter);
        try
          if Fields.Count - 1 < MaxCols - 1 then
            MaxK := Fields.Count - 1
          else
            MaxK := MaxCols - 1;
          for K := 0 to MaxK do
            sgPreview.Cells[K, J] := Fields[K];
          Inc(J);
        finally
          Fields.Free;
        end;
      end;
      
      // 调整行数
      sgPreview.RowCount := J;
      
      lblStatus.Caption := Format('预览: %d行, %d列 (文件总行数: %d)', [J, MaxCols, FileContent.Count]);
      
    finally
      FileContent.Free;
    end;
    
  except
    on E: Exception do
    begin
      LogAndShowError('DataImportForm', '加载预览失败', E, '用户尝试加载预览', True);
      ClearPreview;
    end;
  end;
end;

procedure TfrmDataImport.btnPreviewClick(Sender: TObject);
begin
  LoadPreview;
end;

function TfrmDataImport.ImportData: Boolean;
begin
  Result := False;
  
  if not FileExists(edtFileName.Text) then
  begin
    LogAndShowWarning('DataImportForm', '请选择一个有效的文件', '用户尝试导入但文件不存在');
    Exit;
  end;

  if not Assigned(FTargetDataSet) then
  begin
    LogAndShowError('DataImportForm', '目标数据集未指定', nil, '用户尝试导入数据', True);
    Exit;
  end;
  
  try
    // 这里需要实现具体的导入逻辑
    // 根据目标数据集的类型进行相应的导入操作
    LogInfo('DataImportForm', '导入功能需要根据具体的数据库类型实现', '用户尝试导入数据');
    Result := True;

  except
    on E: Exception do
    begin
      LogAndShowError('DataImportForm', '导入失败', E, '用户尝试导入数据', True);
      Result := False;
    end;
  end;
end;

procedure TfrmDataImport.btnOKClick(Sender: TObject);
begin
  if ImportData then
    ModalResult := mrOK;
end;

procedure TfrmDataImport.btnCancelClick(Sender: TObject);
begin
  ModalResult := mrCancel;
end;

end.
