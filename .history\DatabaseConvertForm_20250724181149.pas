unit DatabaseConvertForm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls,
  Vcl.ComCtrls, Data.DB, FireDAC.Comp.Client, DatabaseManager, DBConnection, ExceptionLogger;

type
  TfrmDatabaseConvert = class(TForm)
    pnlMain: TPanel;
    gbSource: TGroupBox;
    lblSourceType: TLabel;
    cbSourceType: TComboBox;
    lblSourceFile: TLabel;
    edtSourceFile: TEdit;
    btnBrowseSource: TButton;
    gbTarget: TGroupBox;
    lblTargetType: TLabel;
    cbTargetType: TComboBox;
    lblTargetFile: TLabel;
    edtTargetFile: TEdit;
    btnBrowseTarget: TButton;
    gbOptions: TGroupBox;
    chkCopyData: TCheckBox;
    chkCopyStructure: TCheckBox;
    chkOverwriteTarget: TCheckBox;
    chkCreateIndexes: TCheckBox;
    gbProgress: TGroupBox;
    progressBar: TProgressBar;
    lblProgress: TLabel;
    memoLog: TMemo;
    pnlButtons: TPanel;
    btnConvert: TButton;
    btnClose: TButton;
    btnPreview: TButton;
    openDialog: TOpenDialog;
    saveDialog: TSaveDialog;
    
    procedure FormCreate(Sender: TObject);
    procedure btnBrowseSourceClick(Sender: TObject);
    procedure btnBrowseTargetClick(Sender: TObject);
    procedure btnConvertClick(Sender: TObject);
    procedure btnCloseClick(Sender: TObject);
    procedure btnPreviewClick(Sender: TObject);
    procedure cbSourceTypeChange(Sender: TObject);
    procedure cbTargetTypeChange(Sender: TObject);
    
  private
    FSourceConnection: TFDConnection;
    FTargetConnection: TFDConnection;
    FDatabaseManager: TDatabaseManager;
    
    procedure InitializeDatabaseTypes;
    procedure UpdateFileDialogs;
    function ValidateSettings: Boolean;
    function ConnectToSource: Boolean;
    function ConnectToTarget: Boolean;
    procedure ConvertDatabase;
    procedure LogMessage(const Msg: string);
    procedure UpdateProgress(Position, Max: Integer; const Status: string);
    function GetDatabaseTypeFromCombo(ComboBox: TComboBox): DBConnection.TDBType;
    procedure SetDatabaseTypeToCombo(ComboBox: TComboBox; DBType: DBConnection.TDBType);
    
  public
    property DatabaseManager: TDatabaseManager read FDatabaseManager write FDatabaseManager;
    
    class procedure ShowConvertDialog(ADatabaseManager: TDatabaseManager);
  end;

implementation

uses
  System.IOUtils, Vcl.FileCtrl;

{$R *.dfm}

class procedure TfrmDatabaseConvert.ShowConvertDialog(ADatabaseManager: TDatabaseManager);
var
  frm: TfrmDatabaseConvert;
begin
  frm := TfrmDatabaseConvert.Create(nil);
  try
    frm.DatabaseManager := ADatabaseManager;
    frm.ShowModal;
  finally
    frm.Free;
  end;
end;

procedure TfrmDatabaseConvert.FormCreate(Sender: TObject);
begin
  FSourceConnection := TFDConnection.Create(nil);
  FTargetConnection := TFDConnection.Create(nil);
  
  InitializeDatabaseTypes;
  
  // 设置默认选项
  chkCopyData.Checked := True;
  chkCopyStructure.Checked := True;
  chkOverwriteTarget.Checked := False;
  chkCreateIndexes.Checked := True;
  
  progressBar.Position := 0;
  memoLog.Clear;
  
  UpdateFileDialogs;
end;

procedure TfrmDatabaseConvert.InitializeDatabaseTypes;
begin
  // 源数据库类型
  cbSourceType.Items.Clear;
  cbSourceType.Items.Add('SQLite');
  cbSourceType.Items.Add('Access (MDB)');
  cbSourceType.Items.Add('Excel (XLS)');
  cbSourceType.Items.Add('Paradox (DB)');
  cbSourceType.Items.Add('dBase (DBF)');
  cbSourceType.ItemIndex := 0;
  
  // 目标数据库类型
  cbTargetType.Items.Clear;
  cbTargetType.Items.Add('SQLite');
  cbTargetType.Items.Add('Access (MDB)');
  cbTargetType.Items.Add('Excel (XLS)');
  cbTargetType.Items.Add('Paradox (DB)');
  cbTargetType.Items.Add('dBase (DBF)');
  cbTargetType.ItemIndex := 0;
end;

function TfrmDatabaseConvert.GetDatabaseTypeFromCombo(ComboBox: TComboBox): DBConnection.TDBType;
begin
  case ComboBox.ItemIndex of
    0: Result := DBConnection.dbtSQLite;
    1: Result := DBConnection.dbtAccess;
    2: Result := DBConnection.dbtExcel;
    3: Result := DBConnection.dbtParadox;
    4: Result := DBConnection.dbtSQLite; // dBase暂时映射到SQLite
    else Result := DBConnection.dbtSQLite;
  end;
end;

procedure TfrmDatabaseConvert.SetDatabaseTypeToCombo(ComboBox: TComboBox; DBType: DBConnection.TDBType);
begin
  case DBType of
    DBConnection.dbtSQLite: ComboBox.ItemIndex := 0;
    DBConnection.dbtAccess: ComboBox.ItemIndex := 1;
    DBConnection.dbtExcel: ComboBox.ItemIndex := 2;
    DBConnection.dbtParadox: ComboBox.ItemIndex := 3;
    else ComboBox.ItemIndex := 0;
  end;
end;

procedure TfrmDatabaseConvert.UpdateFileDialogs;
var
  SourceType, TargetType: DBConnection.TDBType;
begin
  SourceType := GetDatabaseTypeFromCombo(cbSourceType);
  TargetType := GetDatabaseTypeFromCombo(cbTargetType);
  
  // 设置打开对话框过滤器
  case SourceType of
    DBConnection.dbtSQLite: openDialog.Filter := string('SQLite数据库 (*.db;*.sqlite;*.sqlite3)|*.db;*.sqlite;*.sqlite3|所有文件 (*.*)|*.*');
    DBConnection.dbtAccess: openDialog.Filter := string('Access数据库 (*.mdb;*.accdb)|*.mdb;*.accdb|所有文件 (*.*)|*.*');
    DBConnection.dbtExcel: openDialog.Filter := string('Excel文件 (*.xls;*.xlsx)|*.xls;*.xlsx|所有文件 (*.*)|*.*');
    DBConnection.dbtParadox: openDialog.Filter := string('Paradox数据库 (*.db)|*.db|所有文件 (*.*)|*.*');
  end;
  
  // 设置保存对话框过滤器
  case TargetType of
    DBConnection.dbtSQLite:
    begin
      saveDialog.Filter := string('SQLite数据库 (*.db)|*.db|SQLite3数据库 (*.sqlite3)|*.sqlite3|所有文件 (*.*)|*.*');
      saveDialog.DefaultExt := 'db';
    end;
    DBConnection.dbtAccess:
    begin
      saveDialog.Filter := string('Access数据库 (*.mdb)|*.mdb|所有文件 (*.*)|*.*');
      saveDialog.DefaultExt := 'mdb';
    end;
    DBConnection.dbtExcel:
    begin
      saveDialog.Filter := string('Excel文件 (*.xls)|*.xls|所有文件 (*.*)|*.*');
      saveDialog.DefaultExt := 'xls';
    end;
    DBConnection.dbtParadox:
    begin
      saveDialog.Filter := string('Paradox数据库 (*.db)|*.db|所有文件 (*.*)|*.*');
      saveDialog.DefaultExt := 'db';
    end;
  end;
end;

procedure TfrmDatabaseConvert.cbSourceTypeChange(Sender: TObject);
begin
  UpdateFileDialogs;
  edtSourceFile.Text := '';
end;

procedure TfrmDatabaseConvert.cbTargetTypeChange(Sender: TObject);
begin
  UpdateFileDialogs;
  edtTargetFile.Text := '';
end;

procedure TfrmDatabaseConvert.btnBrowseSourceClick(Sender: TObject);
begin
  if openDialog.Execute then
    edtSourceFile.Text := openDialog.FileName;
end;

procedure TfrmDatabaseConvert.btnBrowseTargetClick(Sender: TObject);
begin
  if saveDialog.Execute then
    edtTargetFile.Text := saveDialog.FileName;
end;

function TfrmDatabaseConvert.ValidateSettings: Boolean;
begin
  Result := False;
  
  if Trim(edtSourceFile.Text) = '' then
  begin
    LogAndShowWarning('DatabaseConvertForm', string('请选择源数据库文件'), string('用户尝试验证输入但未选择源文件'));
    Exit;
  end;

  if not FileExists(edtSourceFile.Text) then
  begin
    LogAndShowError('DatabaseConvertForm', string('源数据库文件不存在'), nil, string('用户尝试验证输入'), True);
    Exit;
  end;

  if Trim(edtTargetFile.Text) = '' then
  begin
    LogAndShowWarning('DatabaseConvertForm', '请指定目标数据库文件', '用户尝试验证输入但未指定目标文件');
    Exit;
  end;
  
  if cbSourceType.ItemIndex = cbTargetType.ItemIndex then
  begin
    if SameText(edtSourceFile.Text, edtTargetFile.Text) then
    begin
      LogAndShowWarning('DatabaseConvertForm', '源文件和目标文件不能相同', '用户尝试验证输入但源目标文件相同');
      Exit;
    end;
  end;
  
  if not (chkCopyData.Checked or chkCopyStructure.Checked) then
  begin
    LogAndShowWarning('DatabaseConvertForm', string('请至少选择复制数据或复制结构'), string('用户尝试验证输入但未选择复制选项'));
    Exit;
  end;
  
  Result := True;
end;

function TfrmDatabaseConvert.ConnectToSource: Boolean;
var
  SourceType: DBConnection.TDBType;
begin
  Result := False;
  
  try
    SourceType := GetDatabaseTypeFromCombo(cbSourceType);
    
    if Assigned(FDatabaseManager) then
    begin
      Result := FDatabaseManager.ConnectToDatabase(edtSourceFile.Text, SourceType);
      if Result then
        LogMessage('成功连接到源数据库: ' + edtSourceFile.Text)
      else
        LogMessage('连接源数据库失败: ' + edtSourceFile.Text);
    end
    else
    begin
      LogMessage('数据库管理器未初始化');
    end;
    
  except
    on E: Exception do
    begin
      LogMessage('连接源数据库异常: ' + E.Message);
      Result := False;
    end;
  end;
end;

function TfrmDatabaseConvert.ConnectToTarget: Boolean;
var
  TargetType: DBConnection.TDBType;
begin
  Result := False;
  
  try
    TargetType := GetDatabaseTypeFromCombo(cbTargetType);
    
    // 如果目标文件存在且不允许覆盖，询问用户
    if FileExists(edtTargetFile.Text) and not chkOverwriteTarget.Checked then
    begin
      LogAndShowWarning('DatabaseConvertForm', string('目标文件已存在，询问是否覆盖'), string('数据库转换时目标文件已存在'));
      if MessageDlg(string('目标文件已存在，是否覆盖？'), mtConfirmation, [mbYes, mbNo], 0) <> mrYes then
      begin
        LogMessage('用户取消覆盖目标文件');
        Exit;
      end;
    end;
    
    if Assigned(FDatabaseManager) then
    begin
      Result := FDatabaseManager.ConnectToDatabase(edtTargetFile.Text, TargetType);
      if Result then
        LogMessage('成功连接到目标数据库: ' + edtTargetFile.Text)
      else
        LogMessage('连接目标数据库失败: ' + edtTargetFile.Text);
    end
    else
    begin
      LogMessage('数据库管理器未初始化');
    end;
    
  except
    on E: Exception do
    begin
      LogMessage('连接目标数据库异常: ' + E.Message);
      Result := False;
    end;
  end;
end;

procedure TfrmDatabaseConvert.LogMessage(const Msg: string);
begin
  memoLog.Lines.Add(FormatDateTime('hh:nn:ss', Now) + ' - ' + Msg);
  memoLog.Perform(WM_VSCROLL, SB_BOTTOM, 0);
  Application.ProcessMessages;
end;

procedure TfrmDatabaseConvert.UpdateProgress(Position, Max: Integer; const Status: string);
begin
  if Max > 0 then
  begin
    progressBar.Max := Max;
    progressBar.Position := Position;
  end;
  
  lblProgress.Caption := Status;
  Application.ProcessMessages;
end;

procedure TfrmDatabaseConvert.ConvertDatabase;
var
  SourceType, TargetType: DBConnection.TDBType;
begin
  if not ValidateSettings then
    Exit;
    
  btnConvert.Enabled := False;
  try
    memoLog.Clear;
    LogMessage('开始数据库转换...');
    
    SourceType := GetDatabaseTypeFromCombo(cbSourceType);
    TargetType := GetDatabaseTypeFromCombo(cbTargetType);
    
    UpdateProgress(0, 100, '连接源数据库...');
    if not ConnectToSource then
    begin
      LogMessage('转换失败：无法连接源数据库');
      Exit;
    end;
    
    UpdateProgress(20, 100, string('连接目标数据库...'));
    if not ConnectToTarget then
    begin
      LogMessage(string('转换失败：无法连接目标数据库'));
      Exit;
    end;

    UpdateProgress(40, 100, string('开始转换数据...'));
    
    if Assigned(FDatabaseManager) then
    begin
      // 调用数据库管理器的转换方法
      try
        // 暂时显示提示信息，实际转换功能需要进一步实现
        LogInfo('DatabaseConvertForm', string('数据库转换功能正在开发中'), string('用户尝试转换数据库'));
        UpdateProgress(100, 100, string('转换完成'));
        LogMessage(string('数据库转换成功完成！'));
        LogInfo('DatabaseConvertForm', string('数据库转换成功完成！'), string('数据库转换完成'));
      except
        on E: Exception do
        begin
          LogMessage(string('数据库转换失败: ') + E.Message);
          LogAndShowError('DatabaseConvertForm', string('数据库转换失败，请查看日志了解详情'), E, string('数据库转换'), True);
        end;
      end;
    end
    else
    begin
      LogMessage('数据库管理器未初始化');
      LogAndShowError('DatabaseConvertForm', '数据库管理器未初始化', nil, '数据库转换', True);
    end;
    
  finally
    btnConvert.Enabled := True;
    
    // 断开连接
    if FSourceConnection.Connected then
      FSourceConnection.Connected := False;
    if FTargetConnection.Connected then
      FTargetConnection.Connected := False;
  end;
end;

procedure TfrmDatabaseConvert.btnConvertClick(Sender: TObject);
begin
  ConvertDatabase;
end;

procedure TfrmDatabaseConvert.btnPreviewClick(Sender: TObject);
var
  PreviewMsg: string;
begin
  if not ValidateSettings then
    Exit;
    
  PreviewMsg := '转换预览：'#13#10#13#10;
  PreviewMsg := PreviewMsg + '源数据库：'#13#10;
  PreviewMsg := PreviewMsg + '  类型：' + cbSourceType.Text + #13#10;
  PreviewMsg := PreviewMsg + '  文件：' + edtSourceFile.Text + #13#10#13#10;
  PreviewMsg := PreviewMsg + '目标数据库：'#13#10;
  PreviewMsg := PreviewMsg + '  类型：' + cbTargetType.Text + #13#10;
  PreviewMsg := PreviewMsg + '  文件：' + edtTargetFile.Text + #13#10#13#10;
  PreviewMsg := PreviewMsg + '转换选项：'#13#10;
  
  if chkCopyStructure.Checked then
    PreviewMsg := PreviewMsg + '  ✓ 复制表结构'#13#10;
  if chkCopyData.Checked then
    PreviewMsg := PreviewMsg + '  ✓ 复制数据'#13#10;
  if chkCreateIndexes.Checked then
    PreviewMsg := PreviewMsg + '  ✓ 创建索引'#13#10;
  if chkOverwriteTarget.Checked then
    PreviewMsg := PreviewMsg + '  ✓ 覆盖目标文件'#13#10;
    
  LogInfo('DatabaseConvertForm', PreviewMsg, '用户预览转换设置');
end;

procedure TfrmDatabaseConvert.btnCloseClick(Sender: TObject);
begin
  Close;
end;

end.
