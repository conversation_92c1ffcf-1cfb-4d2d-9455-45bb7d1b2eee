object frmDataImport: TfrmDataImport
  Left = 0
  Top = 0
  Caption = '数据导入'
  ClientHeight = 600
  ClientWidth = 800
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poScreenCenter
  OnCreate = FormCreate
  PixelsPerInch = 96
  TextHeight = 13
  object pnlMain: TPanel
    Left = 0
    Top = 0
    Width = 800
    Height = 600
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    object lblPreview: TLabel
      Left = 16
      Top = 200
      Width = 48
      Height = 13
      Caption = '数据预览:'
    end
    object lblStatus: TLabel
      Left = 16
      Top = 530
      Width = 200
      Height = 13
      Caption = '准备预览数据'
    end
    object gbFileOptions: TGroupBox
      Left = 16
      Top = 16
      Width = 400
      Height = 80
      Caption = '文件选项'
      TabOrder = 0
      object lblFileName: TLabel
        Left = 16
        Top = 24
        Width = 48
        Height = 13
        Caption = '文件名:'
      end
      object lblEncoding: TLabel
        Left = 16
        Top = 52
        Width = 48
        Height = 13
        Caption = '编码:'
      end
      object edtFileName: TEdit
        Left = 80
        Top = 21
        Width = 240
        Height = 21
        TabOrder = 0
        OnChange = edtFileNameChange
      end
      object btnBrowse: TButton
        Left = 330
        Top = 19
        Width = 60
        Height = 25
        Caption = '浏览...'
        TabOrder = 1
        OnClick = btnBrowseClick
      end
      object cbEncoding: TComboBox
        Left = 80
        Top = 49
        Width = 100
        Height = 21
        Style = csDropDownList
        TabOrder = 2
      end
    end
    object gbDelimiter: TGroupBox
      Left = 430
      Top = 16
      Width = 200
      Height = 120
      Caption = '分隔符设置'
      TabOrder = 1
      object rbComma: TRadioButton
        Left = 16
        Top = 24
        Width = 113
        Height = 17
        Caption = '逗号 (,)'
        Checked = True
        TabOrder = 0
        TabStop = True
      end
      object rbSemicolon: TRadioButton
        Left = 16
        Top = 48
        Width = 113
        Height = 17
        Caption = '分号 (;)'
        TabOrder = 1
      end
      object rbTab: TRadioButton
        Left = 16
        Top = 72
        Width = 113
        Height = 17
        Caption = '制表符 (Tab)'
        TabOrder = 2
      end
      object rbCustom: TRadioButton
        Left = 16
        Top = 96
        Width = 65
        Height = 17
        Caption = '自定义:'
        TabOrder = 3
        OnClick = rbCustomClick
      end
      object edtCustomDelimiter: TEdit
        Left = 87
        Top = 94
        Width = 50
        Height = 21
        Enabled = False
        MaxLength = 5
        TabOrder = 4
      end
      object btnAutoDetect: TButton
        Left = 150
        Top = 20
        Width = 40
        Height = 50
        Caption = '自动检测'
        TabOrder = 5
        OnClick = btnAutoDetectClick
      end
    end
    object gbImportOptions: TGroupBox
      Left = 650
      Top = 16
      Width = 130
      Height = 120
      Caption = '导入选项'
      TabOrder = 2
      object chkFirstRowHeaders: TCheckBox
        Left = 16
        Top = 24
        Width = 97
        Height = 17
        Caption = '首行为表头'
        Checked = True
        State = cbChecked
        TabOrder = 0
      end
      object chkSkipEmptyLines: TCheckBox
        Left = 16
        Top = 48
        Width = 97
        Height = 17
        Caption = '跳过空行'
        Checked = True
        State = cbChecked
        TabOrder = 1
      end
      object chkTrimSpaces: TCheckBox
        Left = 16
        Top = 72
        Width = 97
        Height = 17
        Caption = #21435#38500#31354#26684
        Checked = True
        State = cbChecked
        TabOrder = 2
      end
    end
    object pnlPreview: TPanel
      Left = 16
      Top = 220
      Width = 764
      Height = 300
      BevelOuter = bvLowered
      TabOrder = 3
      object sgPreview: TStringGrid
        Left = 1
        Top = 1
        Width = 762
        Height = 298
        Align = alClient
        ColCount = 1
        DefaultColWidth = 100
        DefaultRowHeight = 20
        RowCount = 1
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
        TabOrder = 0
      end
    end
    object pnlButtons: TPanel
      Left = 0
      Top = 550
      Width = 800
      Height = 50
      Align = alBottom
      BevelOuter = bvNone
      TabOrder = 4
      object btnPreview: TButton
        Left = 16
        Top = 12
        Width = 75
        Height = 25
        Caption = #39044#35272
        TabOrder = 0
        OnClick = btnPreviewClick
      end
      object btnOK: TButton
        Left = 630
        Top = 12
        Width = 75
        Height = 25
        Caption = #23548#20837
        Default = True
        TabOrder = 1
        OnClick = btnOKClick
      end
      object btnCancel: TButton
        Left = 715
        Top = 12
        Width = 75
        Height = 25
        Cancel = True
        Caption = #21462#28040
        TabOrder = 2
        OnClick = btnCancelClick
      end
    end
  end
  object openDialog: TOpenDialog
    DefaultExt = 'csv'
    Filter = 
      'CSV'#25991#20214' (*.csv)|*.csv|'#25991#26412#25991#20214' (*.txt)|*.txt|Excel'#25991#20214' (*.x' +
      'ls)|*.xls|'#25152#26377#25991#20214' (*.*)|*.*'
    Left = 720
    Top = 160
  end
end
