unit DataExportForm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls,
  Vcl.ComCtrls, Data.DB, System.StrUtils, ExceptionLogger;

type
  TfrmDataExport = class(TForm)
    pnlMain: TPanel;
    gbExportOptions: TGroupBox;
    rbCSV: TRadioButton;
    rbTXT: TRadioButton;
    rbXLS: TRadioButton;
    gbDelimiter: TGroupBox;
    rbComma: TRadioButton;
    rbSemicolon: TRadioButton;
    rbTab: TRadioButton;
    rbCustom: TRadioButton;
    edtCustomDelimiter: TEdit;
    gbOtherOptions: TGroupBox;
    chkIncludeHeaders: TCheckBox;
    chkQuoteStrings: TCheckBox;
    chkUTF8Encoding: TCheckBox;
    pnlButtons: TPanel;
    btnOK: TButton;
    btnCancel: TButton;
    btnPreview: TButton;
    lblFileName: TLabel;
    edtFileName: TEdit;
    btnBrowse: TButton;
    memoPreview: TMemo;
    lblPreview: TLabel;
    saveDialog: TSaveDialog;
    
    procedure FormCreate(Sender: TObject);
    procedure rbCustomClick(Sender: TObject);
    procedure btnBrowseClick(Sender: TObject);
    procedure btnPreviewClick(Sender: TObject);
    procedure btnOKClick(Sender: TObject);
    procedure btnCancelClick(Sender: TObject);
    procedure rbCSVClick(Sender: TObject);
    procedure rbTXTClick(Sender: TObject);
    procedure rbXLSClick(Sender: TObject);
    
  private
    FDataSet: TDataSet;
    FTableName: string;
    
    function GetDelimiter: string;
    function GetFileExtension: string;
    procedure UpdateDelimiterOptions;
    procedure GeneratePreview;
    function ExportData: Boolean;
    function ExportToTextFile: Boolean;
    
  public
    property DataSet: TDataSet read FDataSet write FDataSet;
    property TableName: string read FTableName write FTableName;
    
    class function ShowExportDialog(ADataSet: TDataSet; const ATableName: string): Boolean;
  end;

implementation

uses
  UnifiedExcelProcessor_POI, System.IOUtils;

{$R *.dfm}

class function TfrmDataExport.ShowExportDialog(ADataSet: TDataSet; const ATableName: string): Boolean;
var
  frm: TfrmDataExport;
begin
  Result := False;
  frm := TfrmDataExport.Create(nil);
  try
    frm.DataSet := ADataSet;
    frm.TableName := ATableName;
    frm.edtFileName.Text := ATableName + '.csv';
    Result := (frm.ShowModal = mrOK);
  finally
    frm.Free;
  end;
end;

procedure TfrmDataExport.FormCreate(Sender: TObject);
begin
  // 设置默认选项
  rbCSV.Checked := True;
  rbComma.Checked := True;
  chkIncludeHeaders.Checked := True;
  chkQuoteStrings.Checked := True;
  chkUTF8Encoding.Checked := True;
  
  // 设置保存对话框
  saveDialog.Filter := string('CSV文件 (*.csv)|*.csv|文本文件 (*.txt)|*.txt|Excel文件 (*.xls)|*.xls|所有文件 (*.*)|*.*');
  saveDialog.DefaultExt := 'csv';
  
  UpdateDelimiterOptions;
end;

procedure TfrmDataExport.rbCSVClick(Sender: TObject);
begin
  UpdateDelimiterOptions;
  edtFileName.Text := ChangeFileExt(edtFileName.Text, '.csv');
end;

procedure TfrmDataExport.rbTXTClick(Sender: TObject);
begin
  UpdateDelimiterOptions;
  edtFileName.Text := ChangeFileExt(edtFileName.Text, '.txt');
end;

procedure TfrmDataExport.rbXLSClick(Sender: TObject);
begin
  UpdateDelimiterOptions;
  edtFileName.Text := ChangeFileExt(edtFileName.Text, '.xls');
end;

procedure TfrmDataExport.UpdateDelimiterOptions;
begin
  // XLS格式不需要分隔符选项
  gbDelimiter.Enabled := not rbXLS.Checked;
  
  if rbXLS.Checked then
  begin
    chkQuoteStrings.Enabled := False;
    chkQuoteStrings.Checked := False;
  end
  else
  begin
    chkQuoteStrings.Enabled := True;
  end;
end;

procedure TfrmDataExport.rbCustomClick(Sender: TObject);
begin
  edtCustomDelimiter.Enabled := rbCustom.Checked;
  if rbCustom.Checked then
    edtCustomDelimiter.SetFocus;
end;

procedure TfrmDataExport.btnBrowseClick(Sender: TObject);
begin
  saveDialog.FileName := edtFileName.Text;
  
  if rbCSV.Checked then
    saveDialog.FilterIndex := 1
  else if rbTXT.Checked then
    saveDialog.FilterIndex := 2
  else if rbXLS.Checked then
    saveDialog.FilterIndex := 3;
    
  if saveDialog.Execute then
    edtFileName.Text := saveDialog.FileName;
end;

function TfrmDataExport.GetDelimiter: string;
begin
  if rbComma.Checked then
    Result := ','
  else if rbSemicolon.Checked then
    Result := ';'
  else if rbTab.Checked then
    Result := #9
  else if rbCustom.Checked then
    Result := edtCustomDelimiter.Text
  else
    Result := ','; // 默认
end;

function TfrmDataExport.GetFileExtension: string;
begin
  if rbCSV.Checked then
    Result := '.csv'
  else if rbTXT.Checked then
    Result := '.txt'
  else if rbXLS.Checked then
    Result := '.xls'
  else
    Result := '.csv';
end;

procedure TfrmDataExport.GeneratePreview;
var
  Lines: TStringList;
  Line: string;
  I, RowCount: Integer;
  Delimiter: string;
begin
  if not Assigned(FDataSet) or not FDataSet.Active then
  begin
    memoPreview.Lines.Clear;
    memoPreview.Lines.Add('数据集无效或未激活');
    Exit;
  end;

  Lines := TStringList.Create;
  try
    Delimiter := GetDelimiter;
    
    // 添加表头
    if chkIncludeHeaders.Checked then
    begin
      Line := '';
      for I := 0 to FDataSet.FieldCount - 1 do
      begin
        if I > 0 then
          Line := Line + Delimiter;
        
        if chkQuoteStrings.Checked then
          Line := Line + '"' + FDataSet.Fields[I].FieldName + '"'
        else
          Line := Line + FDataSet.Fields[I].FieldName;
      end;
      Lines.Add(Line);
    end;
    
    // 添加数据行（最多预览10行）
    FDataSet.First;
    RowCount := 0;
    while (not FDataSet.Eof) and (RowCount < 10) do
    begin
      Line := '';
      for I := 0 to FDataSet.FieldCount - 1 do
      begin
        if I > 0 then
          Line := Line + Delimiter;
          
        if chkQuoteStrings.Checked then
          Line := Line + '"' + StringReplace(FDataSet.Fields[I].AsString, '"', '""', [rfReplaceAll]) + '"'
        else
          Line := Line + FDataSet.Fields[I].AsString;
      end;
      Lines.Add(Line);
      FDataSet.Next;
      Inc(RowCount);
    end;
    
    if not FDataSet.Eof then
      Lines.Add('... (更多数据)');
    
    memoPreview.Lines.Assign(Lines);
    
  finally
    Lines.Free;
  end;
end;

procedure TfrmDataExport.btnPreviewClick(Sender: TObject);
begin
  if rbXLS.Checked then
  begin
    memoPreview.Lines.Clear;
    memoPreview.Lines.Add('XLS格式预览不可用');
    memoPreview.Lines.Add('将生成Excel文件，包含以下数据：');
    memoPreview.Lines.Add('');
    memoPreview.Lines.Add('表名: ' + FTableName);
    if Assigned(FDataSet) and FDataSet.Active then
    begin
      memoPreview.Lines.Add('记录数: ' + IntToStr(FDataSet.RecordCount));
      memoPreview.Lines.Add('字段数: ' + IntToStr(FDataSet.FieldCount));
    end;
  end
  else
    GeneratePreview;
end;

function TfrmDataExport.ExportData: Boolean;
begin
  Result := False;
  
  if not Assigned(FDataSet) or not FDataSet.Active then
  begin
    LogAndShowError('DataExportForm', '数据集无效或未激活', nil, '用户尝试导出数据', True);
    Exit;
  end;

  if Trim(edtFileName.Text) = '' then
  begin
    LogAndShowWarning('DataExportForm', '请指定输出文件名', '用户尝试导出但未指定文件名');
    Exit;
  end;
  
  try
    if rbXLS.Checked then
    begin
      // 导出为Excel格式
      Result := TExcelAPI.WriteDataSetToExcel(FDataSet, edtFileName.Text, FTableName);
      if Result then
        LogInfo('DataExportForm', '数据已成功导出到Excel文件: ' + edtFileName.Text, '导出数据到Excel')
      else
        LogAndShowError('DataExportForm', '导出Excel文件失败', nil, '导出数据到Excel', True);
    end
    else
    begin
      // 导出为文本格式
      Result := ExportToTextFile;
    end;
    
  except
    on E: Exception do
    begin
      LogAndShowError('DataExportForm', '导出失败', E, '导出数据', True);
      Result := False;
    end;
  end;
end;

procedure TfrmDataExport.btnOKClick(Sender: TObject);
begin
  if ExportData then
    ModalResult := mrOK;
end;

procedure TfrmDataExport.btnCancelClick(Sender: TObject);
begin
  ModalResult := mrCancel;
end;

function TfrmDataExport.ExportToTextFile: Boolean;
var
  FileStream: TFileStream;
  Writer: TStreamWriter;
  Line: string;
  I: Integer;
  Delimiter: string;
  Encoding: TEncoding;
begin
  Result := False;

  try
    // 选择编码
    if chkUTF8Encoding.Checked then
      Encoding := TEncoding.UTF8
    else
      Encoding := TEncoding.Default;

    FileStream := TFileStream.Create(edtFileName.Text, fmCreate);
    try
      Writer := TStreamWriter.Create(FileStream, Encoding);
      try
        Delimiter := GetDelimiter;

        // 写入表头
        if chkIncludeHeaders.Checked then
        begin
          Line := '';
          for I := 0 to FDataSet.FieldCount - 1 do
          begin
            if I > 0 then
              Line := Line + Delimiter;

            if chkQuoteStrings.Checked then
              Line := Line + '"' + FDataSet.Fields[I].FieldName + '"'
            else
              Line := Line + FDataSet.Fields[I].FieldName;
          end;
          Writer.WriteLine(Line);
        end;

        // 写入数据行
        FDataSet.First;
        while not FDataSet.Eof do
        begin
          Line := '';
          for I := 0 to FDataSet.FieldCount - 1 do
          begin
            if I > 0 then
              Line := Line + Delimiter;

            if chkQuoteStrings.Checked then
              Line := Line + '"' + StringReplace(FDataSet.Fields[I].AsString, '"', '""', [rfReplaceAll]) + '"'
            else
              Line := Line + FDataSet.Fields[I].AsString;
          end;
          Writer.WriteLine(Line);
          FDataSet.Next;
        end;

        Result := True;
        LogInfo('DataExportForm', '数据已成功导出到文件: ' + edtFileName.Text, '导出数据到文本文件');

      finally
        Writer.Free;
      end;
    finally
      FileStream.Free;
    end;

  except
    on E: Exception do
    begin
      LogAndShowError('DataExportForm', '导出文件失败', E, '导出数据到文本文件', True);
      Result := False;
    end;
  end;
end;

end.
