program NewDBTool;

{$APPTYPE GUI}
{$WARN IMPLICIT_STRING_CAST OFF}
{$WARN IMPLICIT_STRING_CAST_LOSS OFF}

uses
  System.SysUtils,
  Vcl.Forms,
  {$IFDEF MSWINDOWS}
  Winapi.Windows,
  {$ENDIF}
  MainForm in 'MainForm.pas' {frmMain},
  DBConnection in 'DBConnection.pas',
  DatabaseManager in 'DatabaseManager.pas',
  TableDataManager in 'TableDataManager.pas',
  UIManager in 'UIManager.pas',
  FormulaEngine in 'FormulaEngine.pas',
  DatabaseHistory in 'DatabaseHistory.pas',
  CustomDBGrid in 'CustomDBGrid.pas',
  LegendColorPalette in 'LegendColorPalette.pas',
  ColorPickerForm in 'ColorPickerForm.pas' {frmColorPicker},
  EquipNeedRuleForm in 'EquipNeedRuleForm.pas' {frmEquipNeedRule},
  CleanConfigForm in 'CleanConfigForm.pas' {frmCleanConfig},
  UnifiedExcelProcessor_POI in 'UnifiedExcelProcessor_POI.pas',
  DataExportForm in 'DataExportForm.pas' {frmDataExport},
  DataImportForm in 'DataImportForm.pas' {frmDataImport},
  DatabaseSearchForm in 'DatabaseSearchForm.pas' {frmDatabaseSearch},
  DatabaseConvertForm in 'DatabaseConvertForm.pas' {frmDatabaseConvert},
  ExceptionLogger in 'ExceptionLogger.pas',
  ExceptionLogForm in 'ExceptionLogForm.pas' {frmExceptionLog},
  // FireDAC components
  FireDAC.Phys,
  FireDAC.Phys.SQLite,
  FireDAC.Phys.SQLiteDef,
  FireDAC.Phys.ODBC,
  FireDAC.Phys.ODBCDef,
  FireDAC.Stan.Intf,
  FireDAC.Stan.Option,
  FireDAC.Stan.Error,
  FireDAC.UI.Intf,
  FireDAC.Phys.Intf,
  FireDAC.Stan.Def,
  FireDAC.Stan.Pool,
  FireDAC.Stan.Async,
  FireDAC.Stan.ExprFuncs,
  FireDAC.VCLUI.Wait,
  FireDAC.Comp.Client,
  FireDAC.Comp.DataSet,
  FireDAC.DApt;

begin
  Application.Initialize;
  Application.MainFormOnTaskbar := True;
  Application.CreateForm(TfrmMain, frmMain);
  Application.Run;
end.
