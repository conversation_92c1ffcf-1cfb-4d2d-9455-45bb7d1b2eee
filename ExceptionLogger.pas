unit ExceptionLogger;

interface

uses
  System.SysUtils, System.Classes, System.Generics.Collections, System.IOUtils,
  System.IniFiles, Vcl.Controls, Vcl.ExtCtrls, Vcl.Graphics, Vcl.Forms,
  Winapi.Windows, System.Types;

type
  // 日志等级
  TLogLevel = (llInfo, llWarning, llError, llCritical);

  // 异常记录结构
  TExceptionRecord = record
    ID: Integer;
    Timestamp: TDateTime;
    Level: TLogLevel;
    Module: string;
    Message: string;
    ExceptionClass: string;
    ErrorCode: Integer;
    StackTrace: string;
    UserAction: string;
    IsRead: Boolean;
  end;

  // 前向声明
  TErrorIndicator = class;

  // 异常日志记录器（单例模式）
  TExceptionLogger = class
  private
    FLogFile: string;
    FRetentionDays: Integer; // -1表示永久保留，>0表示保留天数
    FRecords: TList<TExceptionRecord>;
    FNextID: Integer;
    class var FInstance: TExceptionLogger;
    
    constructor Create;
    procedure InitializeLogFile;
    procedure WriteToFile(const LogEntry: string);
    procedure CleanupOldLogs;
    function GetLogLevelText(Level: TLogLevel): string;
    function FormatLogEntry(const Rec: TExceptionRecord): string;
    
  public
    destructor Destroy; override;
    class function Instance: TExceptionLogger;
    
    // 日志记录方法
    procedure LogError(const Module, Message: string; E: Exception = nil; const UserAction: string = '');
    procedure LogWarning(const Module, Message: string; const UserAction: string = '');
    procedure LogInfo(const Module, Message: string; const UserAction: string = '');
    procedure LogCritical(const Module, Message: string; E: Exception = nil; const UserAction: string = '');
    
    // 记录管理
    function GetRecords: TArray<TExceptionRecord>;
    function GetUnreadCount: Integer;
    procedure MarkAsRead(RecordID: Integer);
    procedure MarkAllAsRead;
    procedure ClearAllRecords;
    procedure ExportToFile(const FileName: string);
    
    // 配置属性
    property RetentionDays: Integer read FRetentionDays write FRetentionDays;
    property LogFile: string read FLogFile;
  end;

  // 错误指示器组件
  TErrorIndicator = class(TPanel)
  private
    FFlashTimer: TTimer;
    FIsFlashing: Boolean;
    FFlashState: Boolean;
    FOnIndicatorClick: TNotifyEvent;
    FNormalColor: TColor;
    FErrorColor: TColor;
    
    procedure FlashTimerTick(Sender: TObject);
    procedure UpdateDisplay;

  protected
    procedure Paint; override;
    procedure Click; override;

  public
    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;
    
    // 控制方法
    procedure StartFlashing;  // 开始闪烁，直到用户点击
    procedure StopFlashing;   // 停止闪烁
    procedure CheckForErrors; // 检查是否有未读错误
    
    // 事件
    property OnIndicatorClick: TNotifyEvent read FOnIndicatorClick write FOnIndicatorClick;
  end;

// 全局便捷函数
procedure LogAndShowError(const Module, Message: string; E: Exception = nil; 
  const UserAction: string = ''; ShowIndicator: Boolean = True);
procedure LogAndShowWarning(const Module, Message: string; const UserAction: string = '');
procedure LogInfo(const Module, Message: string; const UserAction: string = '');

implementation

uses
  Vcl.Dialogs, System.DateUtils;

var
  GlobalErrorIndicator: TErrorIndicator = nil;

// 全局便捷函数实现
procedure LogAndShowError(const Module, Message: string; E: Exception = nil; 
  const UserAction: string = ''; ShowIndicator: Boolean = True);
begin
  TExceptionLogger.Instance.LogError(Module, Message, E, UserAction);
  if ShowIndicator and Assigned(GlobalErrorIndicator) then
    GlobalErrorIndicator.StartFlashing;
end;

procedure LogAndShowWarning(const Module, Message: string; const UserAction: string = '');
begin
  TExceptionLogger.Instance.LogWarning(Module, Message, UserAction);
  if Assigned(GlobalErrorIndicator) then
    GlobalErrorIndicator.CheckForErrors;
end;

procedure LogInfo(const Module, Message: string; const UserAction: string);
begin
  TExceptionLogger.Instance.LogInfo(Module, Message, UserAction);
end;

{ TExceptionLogger }

constructor TExceptionLogger.Create;
begin
  inherited Create;
  FRecords := TList<TExceptionRecord>.Create;
  FRetentionDays := -1; // 默认永久保留
  FNextID := 1;
  InitializeLogFile;
end;

destructor TExceptionLogger.Destroy;
begin
  FRecords.Free;
  inherited;
end;

class function TExceptionLogger.Instance: TExceptionLogger;
begin
  if not Assigned(FInstance) then
    FInstance := TExceptionLogger.Create;
  Result := FInstance;
end;

procedure TExceptionLogger.InitializeLogFile;
var
  LogDir: string;
  ConfigFile: TIniFile;
begin
  // 创建日志目录
  LogDir := TPath.Combine(ExtractFilePath(Application.ExeName), 'Logs');
  if not TDirectory.Exists(LogDir) then
    TDirectory.CreateDirectory(LogDir);
    
  // 设置日志文件名
  FLogFile := TPath.Combine(LogDir, 'NewDBTool_' + FormatDateTime('yyyymmdd', Now) + '.log');
  
  // 读取配置
  ConfigFile := TIniFile.Create(ChangeFileExt(Application.ExeName, '.ini'));
  try
    FRetentionDays := ConfigFile.ReadInteger('异常日志', '保留天数', -1);
  finally
    ConfigFile.Free;
  end;
  
  // 清理旧日志
  CleanupOldLogs;
end;

procedure TExceptionLogger.WriteToFile(const LogEntry: string);
var
  FileStream: TFileStream;
  UTF8Bytes: TBytes;
begin
  try
    UTF8Bytes := TEncoding.UTF8.GetBytes(LogEntry + sLineBreak);
    
    if FileExists(FLogFile) then
      FileStream := TFileStream.Create(FLogFile, fmOpenWrite or fmShareDenyWrite)
    else
      FileStream := TFileStream.Create(FLogFile, fmCreate or fmShareDenyWrite);
      
    try
      FileStream.Seek(0, soFromEnd);
      FileStream.WriteBuffer(UTF8Bytes[0], Length(UTF8Bytes));
    finally
      FileStream.Free;
    end;
  except
    // 忽略日志写入错误，避免递归
  end;
end;

procedure TExceptionLogger.CleanupOldLogs;
var
  LogDir: string;
  Files: TArray<string>;
  FileName: string;
  FileDate: TDateTime;
  CutoffDate: TDateTime;
begin
  if FRetentionDays <= 0 then
    Exit; // 永久保留
    
  LogDir := TPath.GetDirectoryName(FLogFile);
  CutoffDate := Now - FRetentionDays;
  
  try
    Files := TDirectory.GetFiles(LogDir, 'NewDBTool_*.log');
    for FileName in Files do
    begin
      FileDate := TFile.GetCreationTime(FileName);
      if FileDate < CutoffDate then
        TFile.Delete(FileName);
    end;
  except
    // 忽略清理错误
  end;
end;

function TExceptionLogger.GetLogLevelText(Level: TLogLevel): string;
begin
  case Level of
    llInfo: Result := #$4FE1#$606F;        // '信息' 的Unicode编码
    llWarning: Result := #$8B66#$544A;     // '警告' 的Unicode编码
    llError: Result := #$9519#$8BEF;       // '错误' 的Unicode编码
    llCritical: Result := #$4E25#$91CD;    // '严重' 的Unicode编码
  else
    Result := #$672A#$77E5;                // '未知' 的Unicode编码
  end;
end;

function TExceptionLogger.FormatLogEntry(const Rec: TExceptionRecord): string;
begin
  Result := Format('[%s] [%s] [%s] %s', [
    FormatDateTime('yyyy-mm-dd hh:nn:ss', Rec.Timestamp),
    GetLogLevelText(Rec.Level),
    Rec.Module,
    Rec.Message
  ]);
  
  if Rec.ExceptionClass <> '' then
    Result := Result + sLineBreak + '异常类型: ' + Rec.ExceptionClass;
    
  if Rec.UserAction <> '' then
    Result := Result + sLineBreak + '用户操作: ' + Rec.UserAction;
    
  if Rec.StackTrace <> '' then
    Result := Result + sLineBreak + '堆栈跟踪: ' + Rec.StackTrace;
    
  Result := Result + sLineBreak + '---';
end;

procedure TExceptionLogger.LogError(const Module, Message: string; E: Exception; const UserAction: string);
var
  Rec: TExceptionRecord;
begin
  Rec.ID := FNextID;
  Inc(FNextID);
  Rec.Timestamp := Now;
  Rec.Level := llError;
  Rec.Module := Module;
  Rec.Message := Message;
  Rec.UserAction := UserAction;
  Rec.IsRead := False;
  
  if Assigned(E) then
  begin
    Rec.ExceptionClass := E.ClassName;
    Rec.StackTrace := E.StackTrace;
    if E is EOSError then
      Rec.ErrorCode := EOSError(E).ErrorCode
    else
      Rec.ErrorCode := 0;
  end
  else
  begin
    Rec.ExceptionClass := '';
    Rec.StackTrace := '';
    Rec.ErrorCode := 0;
  end;
  
  FRecords.Add(Rec);
  WriteToFile(FormatLogEntry(Rec));
end;

procedure TExceptionLogger.LogWarning(const Module, Message: string; const UserAction: string);
var
  Rec: TExceptionRecord;
begin
  Rec.ID := FNextID;
  Inc(FNextID);
  Rec.Timestamp := Now;
  Rec.Level := llWarning;
  Rec.Module := Module;
  Rec.Message := Message;
  Rec.UserAction := UserAction;
  Rec.ExceptionClass := '';
  Rec.StackTrace := '';
  Rec.ErrorCode := 0;
  Rec.IsRead := False;

  FRecords.Add(Rec);
  WriteToFile(FormatLogEntry(Rec));
end;

procedure TExceptionLogger.LogInfo(const Module, Message: string; const UserAction: string);
var
  Rec: TExceptionRecord;
begin
  Rec.ID := FNextID;
  Inc(FNextID);
  Rec.Timestamp := Now;
  Rec.Level := llInfo;
  Rec.Module := Module;
  Rec.Message := Message;
  Rec.UserAction := UserAction;
  Rec.ExceptionClass := '';
  Rec.StackTrace := '';
  Rec.ErrorCode := 0;
  Rec.IsRead := True; // 信息级别默认已读

  FRecords.Add(Rec);
  WriteToFile(FormatLogEntry(Rec));
end;

procedure TExceptionLogger.LogCritical(const Module, Message: string; E: Exception; const UserAction: string);
var
  Rec: TExceptionRecord;
begin
  Rec.ID := FNextID;
  Inc(FNextID);
  Rec.Timestamp := Now;
  Rec.Level := llCritical;
  Rec.Module := Module;
  Rec.Message := Message;
  Rec.UserAction := UserAction;
  Rec.IsRead := False;

  if Assigned(E) then
  begin
    Rec.ExceptionClass := E.ClassName;
    Rec.StackTrace := E.StackTrace;
    if E is EOSError then
      Rec.ErrorCode := EOSError(E).ErrorCode
    else
      Rec.ErrorCode := 0;
  end
  else
  begin
    Rec.ExceptionClass := '';
    Rec.StackTrace := '';
    Rec.ErrorCode := 0;
  end;

  FRecords.Add(Rec);
  WriteToFile(FormatLogEntry(Rec));
end;

function TExceptionLogger.GetRecords: TArray<TExceptionRecord>;
begin
  Result := FRecords.ToArray;
end;

function TExceptionLogger.GetUnreadCount: Integer;
var
  Rec: TExceptionRecord;
begin
  Result := 0;
  for Rec in FRecords do
  begin
    if not Rec.IsRead and (Rec.Level in [llWarning, llError, llCritical]) then
      Inc(Result);
  end;
end;

procedure TExceptionLogger.MarkAsRead(RecordID: Integer);
var
  i: Integer;
  Rec: TExceptionRecord;
begin
  for i := 0 to FRecords.Count - 1 do
  begin
    if FRecords[i].ID = RecordID then
    begin
      Rec := FRecords[i];
      Rec.IsRead := True;
      FRecords[i] := Rec;
      Break;
    end;
  end;
end;

procedure TExceptionLogger.MarkAllAsRead;
var
  i: Integer;
  Rec: TExceptionRecord;
begin
  for i := 0 to FRecords.Count - 1 do
  begin
    Rec := FRecords[i];
    Rec.IsRead := True;
    FRecords[i] := Rec;
  end;
end;

procedure TExceptionLogger.ClearAllRecords;
begin
  FRecords.Clear;
end;

procedure TExceptionLogger.ExportToFile(const FileName: string);
var
  Lines: TStringList;
  Rec: TExceptionRecord;
begin
  Lines := TStringList.Create;
  try
    Lines.Add('# NewDBTool 异常日志导出');
    Lines.Add('# 导出时间: ' + FormatDateTime('yyyy-mm-dd hh:nn:ss', Now));
    Lines.Add('# 记录总数: ' + IntToStr(FRecords.Count));
    Lines.Add('');

    for Rec in FRecords do
    begin
      Lines.Add(FormatLogEntry(Rec));
    end;

    Lines.SaveToFile(FileName, TEncoding.UTF8);
  finally
    Lines.Free;
  end;
end;

{ TErrorIndicator }

constructor TErrorIndicator.Create(AOwner: TComponent);
begin
  inherited Create(AOwner);

  // 设置基本属性
  Width := 24;
  Height := 24;
  BevelOuter := bvNone;
  Caption := '';
  Cursor := crHandPoint;
  Hint := '点击查看异常日志';
  ShowHint := True;

  // 设置颜色
  FNormalColor := clBtnFace;
  FErrorColor := clRed;
  Color := FNormalColor;

  // 创建闪烁定时器
  FFlashTimer := TTimer.Create(Self);
  FFlashTimer.Interval := 500; // 500毫秒闪烁一次
  FFlashTimer.OnTimer := FlashTimerTick;
  FFlashTimer.Enabled := False;

  FIsFlashing := False;
  FFlashState := False;

  // 设置全局引用
  GlobalErrorIndicator := Self;
end;

destructor TErrorIndicator.Destroy;
begin
  if GlobalErrorIndicator = Self then
    GlobalErrorIndicator := nil;
  FFlashTimer.Free;
  inherited;
end;

procedure TErrorIndicator.FlashTimerTick(Sender: TObject);
begin
  if FIsFlashing then
  begin
    FFlashState := not FFlashState;
    UpdateDisplay;
  end;
end;

procedure TErrorIndicator.UpdateDisplay;
begin
  if FIsFlashing then
  begin
    if FFlashState then
      Color := FErrorColor
    else
      Color := FNormalColor;
  end
  else
  begin
    if TExceptionLogger.Instance.GetUnreadCount > 0 then
      Color := FErrorColor
    else
      Color := FNormalColor;
  end;

  Invalidate;
end;

procedure TErrorIndicator.Paint;
var
  R: TRect;
  CenterX, CenterY: Integer;
begin
  inherited Paint;

  R := ClientRect;
  CenterX := R.Width div 2;
  CenterY := R.Height div 2;

  // 绘制圆形指示器
  Canvas.Brush.Style := bsSolid;
  Canvas.Pen.Style := psSolid;
  Canvas.Pen.Width := 1;

  if TExceptionLogger.Instance.GetUnreadCount > 0 then
  begin
    Canvas.Brush.Color := Color;
    Canvas.Pen.Color := clMaroon;
  end
  else
  begin
    Canvas.Brush.Color := FNormalColor;
    Canvas.Pen.Color := clGray;
  end;

  // 绘制圆形
  Canvas.Ellipse(CenterX - 8, CenterY - 8, CenterX + 8, CenterY + 8);

  // 如果有未读错误，在中心绘制感叹号
  if TExceptionLogger.Instance.GetUnreadCount > 0 then
  begin
    Canvas.Font.Name := 'Arial';
    Canvas.Font.Size := 10;
    Canvas.Font.Style := [fsBold];
    Canvas.Font.Color := clWhite;
    Canvas.Brush.Style := bsClear;

    Canvas.TextOut(CenterX - 3, CenterY - 8, '!');
  end;
end;

procedure TErrorIndicator.Click;
begin
  inherited Click;

  // 停止闪烁
  StopFlashing;

  // 触发点击事件
  if Assigned(FOnIndicatorClick) then
    FOnIndicatorClick(Self);
end;

procedure TErrorIndicator.StartFlashing;
begin
  if TExceptionLogger.Instance.GetUnreadCount > 0 then
  begin
    FIsFlashing := True;
    FFlashState := False;
    FFlashTimer.Enabled := True;
    UpdateDisplay;
  end;
end;

procedure TErrorIndicator.StopFlashing;
begin
  FIsFlashing := False;
  FFlashTimer.Enabled := False;
  UpdateDisplay;
end;

procedure TErrorIndicator.CheckForErrors;
begin
  if TExceptionLogger.Instance.GetUnreadCount > 0 then
  begin
    if not FIsFlashing then
      StartFlashing;
  end
  else
  begin
    StopFlashing;
  end;
end;

// 单元初始化和清理
initialization

finalization
  if Assigned(TExceptionLogger.FInstance) then
    TExceptionLogger.FInstance.Free;

end.
