unit UIManager;

interface

uses
  System.SysUtils, System.Classes, System.Generics.Collections, System.Types,
  Vcl.Controls, Vcl.ComCtrls, Vcl.ExtCtrls, Vcl.StdCtrls, Vcl.Graphics,
  Vcl.ImgList, Vcl.Menus, System.Actions, Vcl.ActnList, DBConnection,
  Data.DB, FireDAC.Comp.Client, DatabaseManager;

type
  // UI管理器类
  TUIManager = class
  private
    FTreeView: TTreeView;
    FListView: TListView;
    FImageList: TImageList;
    FSearchEdit: TEdit;
    FStatusBar: TStatusBar;
    FActionList: TActionList;
    FConnection: TFDConnection;
    FDatabaseList: TObjectList<DatabaseManager.TDatabaseInfo>;

    // 私有方法
    procedure CreateDefaultIcons;
    procedure LoadIconsFromFiles;
    function GetDatabaseIcon(ADBType: DBConnection.TDBType): Integer;
    function GetDatabaseTypeString(ADBType: DBConnection.TDBType): string;

  public
    constructor Create(ATreeView: TTreeView; AListView: TListView; AImageList: TImageList;
      ASearchEdit: TEdit; AStatusBar: TStatusBar; AActionList: TActionList;
      AConnection: TFDConnection; ADatabaseList: TObjectList<DatabaseManager.TDatabaseInfo>);
    destructor Destroy; override;

    // 图标管理
    procedure LoadIcons;

    // TreeView操作
    procedure ClearTreeView;
    procedure ExpandAllNodes;
    procedure CollapseAllNodes;
    function FindNodeByText(const AText: string): TTreeNode;

    // ListView操作
    procedure CreateModernUI;
    procedure LoadModernDatabaseList;
    procedure FilterDatabaseList(const SearchText: string);
    procedure AddDatabaseToModernList(const APath, AName: string; ADBType: DBConnection.TDBType; const Category: string);

    // ListView展开操作
    procedure ToggleListViewItem(Item: TListItem);
    function IsItemExpanded(Item: TListItem): Boolean;
    procedure SetItemExpanded(Item: TListItem; Expanded: Boolean);
    function GetItemLevel(Item: TListItem): Integer;

    // 上下文菜单
    procedure ShowDatabaseContextMenu(const APoint: TPoint; AItem: TListItem);

    // 状态管理
    procedure UpdateStatusBar(const AMessage: string);

    // 事件处理
    procedure OnSearchChange(Sender: TObject);
    procedure HandleTreeViewDoubleClick(Node: TTreeNode; var CurrentTable: string);

    // ListView双击和上下文菜单事件处理

    // 属性
    property TreeView: TTreeView read FTreeView;
    property ListView: TListView read FListView;
    property ImageList: TImageList read FImageList;
  end;

implementation

uses
  Winapi.Windows, System.IOUtils;

constructor TUIManager.Create(ATreeView: TTreeView; AListView: TListView; AImageList: TImageList;
  ASearchEdit: TEdit; AStatusBar: TStatusBar; AActionList: TActionList;
  AConnection: TFDConnection; ADatabaseList: TObjectList<DatabaseManager.TDatabaseInfo>);
begin
  inherited Create;
  FTreeView := ATreeView;
  FListView := AListView;
  FImageList := AImageList;
  FSearchEdit := ASearchEdit;
  FStatusBar := AStatusBar;
  FActionList := AActionList;
  FConnection := AConnection;
  FDatabaseList := ADatabaseList;
end;

destructor TUIManager.Destroy;
begin
  inherited;
end;

procedure TUIManager.LoadIcons;
var
  IconPath: string;
begin
  // 检查FImageList是否已分配
  if not Assigned(FImageList) then
    Exit;

  // 清空现有图标
  FImageList.Clear;

  // 设置图标文件路径
  IconPath := ExtractFilePath(ParamStr(0)) + 'icon\';

  // 尝试从文件加载图标
  if DirectoryExists(IconPath) then
    LoadIconsFromFiles
  else
    CreateDefaultIcons;

  // 如果图标加载失败，使用默认图标
  if FImageList.Count = 0 then
    CreateDefaultIcons;
end;

procedure TUIManager.LoadIconsFromFiles;
begin
  // 从文件加载图标，如果失败则使用默认图标
  CreateDefaultIcons;
end;

procedure TUIManager.CreateDefaultIcons;
begin
  // 创建默认的ImageList图标
  if Assigned(FImageList) then
  begin
    FImageList.Clear;

    // 设置ImageList基本属性
    FImageList.Width := 16;
    FImageList.Height := 16;
    FImageList.ColorDepth := cd32Bit;

    // 这里可以添加默认的图标创建代码
    // 暂时使用系统默认图标或空图标
  end;
end;



procedure TUIManager.ClearTreeView;
begin
  if Assigned(FTreeView) then
    FTreeView.Items.Clear;
end;

procedure TUIManager.ExpandAllNodes;
begin
  if Assigned(FTreeView) then
    FTreeView.FullExpand;
end;

procedure TUIManager.CollapseAllNodes;
begin
  if Assigned(FTreeView) then
    FTreeView.FullCollapse;
end;

function TUIManager.FindNodeByText(const AText: string): TTreeNode;
var
  i: Integer;
begin
  Result := nil;
  if not Assigned(FTreeView) then
    Exit;

  for i := 0 to FTreeView.Items.Count - 1 do
  begin
    if FTreeView.Items[i].Text = AText then
    begin
      Result := FTreeView.Items[i];
      Break;
    end;
  end;
end;

function TUIManager.GetDatabaseIcon(ADBType: DBConnection.TDBType): Integer;
begin
  case ADBType of
    dbtSQLite: Result := 2;
    dbtAccess: Result := 3;
    dbtParadox, dbtParadoxBDE, dbtParadoxDirect: Result := 4;
    dbtExcel: Result := 5;
  else
    Result := 2; // 默认使用SQLite图标
  end;
end;

function TUIManager.GetDatabaseTypeString(ADBType: DBConnection.TDBType): string;
begin
  case ADBType of
    dbtSQLite: Result := string('SQLite数据库');
    dbtAccess: Result := string('Access数据库');
    dbtParadox: Result := string('Paradox数据库');
    dbtParadoxBDE: Result := string('Paradox BDE数据库');
    dbtParadoxDirect: Result := string('Paradox直接访问');
    dbtExcel: Result := string('Excel文件');
  else
    Result := string('未知类型');
  end;
end;

procedure TUIManager.UpdateStatusBar(const AMessage: string);
begin
  if Assigned(FStatusBar) then
    FStatusBar.SimpleText := AMessage;
end;



// 创建现代化UI界面
procedure TUIManager.CreateModernUI;
begin
  // 现代化UI设置，通常在MainForm的FormCreate中调用
  // 配置ListView的显示样式
  if Assigned(FListView) then
  begin
    FListView.ViewStyle := vsReport;
    FListView.RowSelect := True;
    FListView.ReadOnly := True;
    FListView.GridLines := True;
    FListView.HideSelection := False;
    FListView.SmallImages := FImageList;
  end;
end;

procedure TUIManager.LoadModernDatabaseList;
var
  i: Integer;
  DBInfo: DatabaseManager.TDatabaseInfo;
  CategoryItem: TListItem;
  DBItem: TListItem;
  DBDir: string;
  CategoryAdded: Boolean;
begin
  if not Assigned(FListView) or not Assigned(FDatabaseList) then
    Exit;

  // 清空ListView
  FListView.Items.Clear;

  // 设置ListView列
  if FListView.Columns.Count = 0 then
  begin
    with FListView.Columns.Add do
    begin
      Caption := '数据库名';
      Width := 300;
    end;
    with FListView.Columns.Add do
    begin
      Caption := '类型';
      Width := 100;
    end;
    with FListView.Columns.Add do
    begin
      Caption := '路径';
      Width := 200;
    end;
  end;

  // 添加根分类节点
  CategoryItem := FListView.Items.Add;
  CategoryItem.Caption := '[+] 所有数据库';
  CategoryItem.ImageIndex := 0; // 文件夹图标
  CategoryItem.Data := Pointer(0); // 分类标记

  CategoryAdded := False;

  // 遍历数据库列表并添加到ListView
  for i := 0 to FDatabaseList.Count - 1 do
  begin
    DBInfo := FDatabaseList[i];
    DBDir := ExtractFileDir(DBInfo.Path);

    // 检查是否是DB目录下的数据库
    if DBDir.EndsWith('Mud2\DB') or DBDir.EndsWith('Mud2/DB') then
    begin
      if not CategoryAdded then
      begin
        // 添加游戏数据库分类
        CategoryItem := FListView.Items.Add;
        CategoryItem.Caption := string('[+] 游戏数据库');
        CategoryItem.ImageIndex := 1; // 特殊分类图标
        CategoryItem.Data := Pointer(1); // 分类标记
        CategoryAdded := True;
      end;

      // 添加数据库项目
      DBItem := FListView.Items.Add;
      DBItem.Caption := DBInfo.Name;
      DBItem.SubItems.Add(GetDatabaseTypeString(DBInfo.DBType));
      DBItem.SubItems.Add(DBInfo.Path);
      DBItem.ImageIndex := GetDatabaseIcon(DBInfo.DBType);
      DBItem.Data := Pointer(DBInfo); // 存储数据库信息
    end
    else
    begin
      // 其他数据库直接添加到根分类下
      DBItem := FListView.Items.Add;
      DBItem.Caption := DBInfo.Name;
      DBItem.SubItems.Add(GetDatabaseTypeString(DBInfo.DBType));
      DBItem.SubItems.Add(DBInfo.Path);
      DBItem.ImageIndex := GetDatabaseIcon(DBInfo.DBType);
      DBItem.Data := Pointer(DBInfo); // 存储数据库信息
    end;
  end;
end;

procedure TUIManager.FilterDatabaseList(const SearchText: string);
var
  i: Integer;
  Item: TListItem;
  ShowItem: Boolean;
begin
  if not Assigned(FListView) then
    Exit;

  // 如果搜索文本为空，显示所有项目
  if Trim(SearchText) = '' then
  begin
    for i := 0 to FListView.Items.Count - 1 do
      FListView.Items[i].Caption := FListView.Items[i].Caption; // 刷新显示
    Exit;
  end;

  // 过滤项目
  for i := 0 to FListView.Items.Count - 1 do
  begin
    Item := FListView.Items[i];

    // 检查项目标题是否包含搜索文本
    ShowItem := Pos(UpperCase(SearchText), UpperCase(Item.Caption)) > 0;

    // 如果标题不匹配，检查路径是否匹配
    if not ShowItem and Assigned(Item.Data) and (Integer(Item.Data) > 1) then
    begin
      var DBInfo := DatabaseManager.TDatabaseInfo(Item.Data);
      ShowItem := Pos(UpperCase(SearchText), UpperCase(DBInfo.Path)) > 0;
    end;

    // 根据搜索结果显示或隐藏项目
    if not ShowItem then
    begin
      // 这里可以实现隐藏不匹配的项目
      // 暂时保留所有项目，只是不高亮显示
    end;
  end;
end;

procedure TUIManager.AddDatabaseToModernList(const APath, AName: string; ADBType: DBConnection.TDBType; const Category: string);
var
  DBItem: TListItem;
begin
  if not Assigned(FListView) then
    Exit;

  // 添加数据库项目到ListView
  DBItem := FListView.Items.Add;
  DBItem.Caption := AName;
  DBItem.SubItems.Add(GetDatabaseTypeString(ADBType));
  DBItem.SubItems.Add(APath);
  DBItem.ImageIndex := GetDatabaseIcon(ADBType);

  // 创建并存储数据库信息对象
  var DBInfo := DatabaseManager.TDatabaseInfo.Create(APath, AName, ADBType);
  DBItem.Data := Pointer(DBInfo);
end;



procedure TUIManager.ToggleListViewItem(Item: TListItem);
var
  IsExpanded: Boolean;
  i, NextItemIndex: Integer;
  DBInfo: DatabaseManager.TDatabaseInfo;
  TableItem: TListItem;
  DBManager: DBConnection.TDBConnectionManager;
  Tables: TStringList;
begin
  if not Assigned(Item) then
    Exit;

  // 检查项目的Data是否为TDatabaseInfo对象
  if Assigned(Item.Data) and (Integer(Item.Data) > 1) then
  begin
    DBInfo := DatabaseManager.TDatabaseInfo(Item.Data);

    // 检查展开状态
    IsExpanded := IsItemExpanded(Item);

    if IsExpanded then
    begin
      // 折叠：删除子项目
      NextItemIndex := Item.Index + 1;
      while (NextItemIndex < FListView.Items.Count) and
            (GetItemLevel(FListView.Items[NextItemIndex]) > GetItemLevel(Item)) do
      begin
        FListView.Items.Delete(NextItemIndex);
      end;

      // 更新展开状态
      SetItemExpanded(Item, False);
    end
    else
    begin
      // 展开：加载并显示表列表
      if Assigned(FConnection) then
      begin
        // 连接数据库
        try
          if FConnection.Connected then
            FConnection.Close;

          FConnection.Params.Clear;
          case DBInfo.DBType of
            dbtSQLite:
            begin
              FConnection.Params.DriverID := 'SQLite';
              FConnection.Params.Database := DBInfo.Path;
              FConnection.Params.Add('CharacterSet=gb2312');
            end;
            dbtAccess:
            begin
              FConnection.Params.DriverID := 'MSAcc';
              FConnection.Params.Database := DBInfo.Path;
              FConnection.Params.Add('CharacterSet=gb2312');
            end;
            dbtParadox, dbtParadoxDirect:
            begin
              FConnection.Params.DriverID := 'ODBC';
              FConnection.Params.Values['DriverName'] := 'Microsoft dBASE Driver (*.dbf)';
              FConnection.Params.Values['DefaultDir'] := DBInfo.Path;
            end;
          end;

          FConnection.Open;

          // 获取表列表
          DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
          try
            Tables := nil;
            Tables := DBManager.GetTableNames;
            if Assigned(Tables) and (Tables.Count > 0) then
            begin
              Tables.Sort;

              // 添加表项目到列表
              for i := 0 to Tables.Count - 1 do
              begin
                TableItem := FListView.Items.Insert(Item.Index + 1 + i);
                TableItem.Caption := '    ' + Tables[i]; // 缩进显示
                TableItem.ImageIndex := 5; // 表图标
                TableItem.Data := nil; // 表项目不存储数据
              end;
            end;
          finally
            DBManager.Free;
            if Assigned(Tables) then
              Tables.Free;
          end;

        except
          on E: Exception do
          begin
            // 数据库连接失败时的处理
            UpdateStatusBar('数据库连接失败: ' + E.Message);
            Exit;
          end;
        end;
      end;

      // 设置展开状态
      SetItemExpanded(Item, True);
    end;
  end
  else if (Integer(Item.Data) = 0) or (Integer(Item.Data) = 1) then
  begin
    // 处理分类节点的展开/折叠
    IsExpanded := IsItemExpanded(Item);
    SetItemExpanded(Item, not IsExpanded);
  end;
end;

function TUIManager.IsItemExpanded(Item: TListItem): Boolean;
begin
  Result := False;
  if not Assigned(Item) then
    Exit;

  // 检查下一个项目是否为子项目（缩进显示）
  if Item.Index + 1 < FListView.Items.Count then
  begin
    var NextItem := FListView.Items[Item.Index + 1];
    Result := NextItem.Caption.StartsWith('    '); // 检查是否有4个空格缩进
  end;
end;

procedure TUIManager.SetItemExpanded(Item: TListItem; Expanded: Boolean);
begin
  if not Assigned(Item) then
    Exit;

  // 通过修改Caption中的展开/折叠标记来设置状态
  if Expanded then
  begin
    if not Item.Caption.StartsWith('[-] ') then
    begin
      if Item.Caption.StartsWith('[+] ') then
        Item.Caption := '[-] ' + Item.Caption.Substring(4)
      else
        Item.Caption := '[-] ' + Item.Caption;
    end;
  end
  else
  begin
    if not Item.Caption.StartsWith('[+] ') then
    begin
      if Item.Caption.StartsWith('[-] ') then
        Item.Caption := '[+] ' + Item.Caption.Substring(4)
      else
        Item.Caption := '[+] ' + Item.Caption;
    end;
  end;
end;

function TUIManager.GetItemLevel(Item: TListItem): Integer;
var
  Caption: string;
  i: Integer;
begin
  Result := 0;
  if not Assigned(Item) then
    Exit;

  Caption := Item.Caption;

  // 计算前导空格的数量
  for i := 1 to Length(Caption) do
  begin
    if Caption[i] = ' ' then
      Inc(Result)
    else
      Break;
  end;

  // 每4个空格为一个层级
  Result := Result div 4;
end;

procedure TUIManager.ShowDatabaseContextMenu(const APoint: TPoint; AItem: TListItem);
var
  PopupMenu: TPopupMenu;
  MenuItem: TMenuItem;
begin
  if not Assigned(AItem) then
    Exit;

  // 创建上下文菜单
  PopupMenu := TPopupMenu.Create(nil);
  try
    // 根据项目类型创建不同的菜单项
    if Assigned(AItem.Data) and (Integer(AItem.Data) > 1) then
    begin
      // 数据库项目菜单
      MenuItem := TMenuItem.Create(PopupMenu);
      MenuItem.Caption := string('连接数据库');
      MenuItem.Tag := Integer(AItem.Data);
      PopupMenu.Items.Add(MenuItem);

      MenuItem := TMenuItem.Create(PopupMenu);
      MenuItem.Caption := '-';
      PopupMenu.Items.Add(MenuItem);

      MenuItem := TMenuItem.Create(PopupMenu);
      MenuItem.Caption := '从历史中删除';
      MenuItem.Tag := Integer(AItem.Data);
      PopupMenu.Items.Add(MenuItem);
    end
    else if (Integer(AItem.Data) = 0) or (Integer(AItem.Data) = 1) then
    begin
      // 分类菜单
      MenuItem := TMenuItem.Create(PopupMenu);
      MenuItem.Caption := string('刷新数据库列表');
      MenuItem.Tag := Integer(AItem.Data);
      PopupMenu.Items.Add(MenuItem);
    end;

    // 显示菜单
    if PopupMenu.Items.Count > 0 then
    begin
      PopupMenu.Popup(APoint.X, APoint.Y);
    end;

  finally
    PopupMenu.Free;
  end;
end;

procedure TUIManager.OnSearchChange(Sender: TObject);
begin
  if Assigned(FSearchEdit) then
    FilterDatabaseList(FSearchEdit.Text);
end;

procedure TUIManager.HandleTreeViewDoubleClick(Node: TTreeNode; var CurrentTable: string);
begin
  if not Assigned(Node) then
    Exit;

  // 检查是否是表节点（Level = 1）
  if Node.Level = 1 then
  begin
    if not Assigned(Node.Data) then
    begin
      // 这是表节点，更新当前表名
      CurrentTable := Node.Text;
      UpdateStatusBar('正在加载表数据: ' + CurrentTable);

      // 注意：实际的表数据加载应该在MainForm中调用TableDataManager
      // 这里只是更新状态，实际加载在MainForm.tvDatabasesDblClick中进行
    end;
  end;
end;

end.
