unit DatabaseManager;

interface

uses
  System.SysUtils, System.Classes, System.IniFiles, System.Generics.Collections,
  Vcl.ComCtrls, Vcl.Dialogs, Data.DB, FireDAC.Comp.Client, DBConnection, System.IOUtils;

type
  // 数据库信息类
  TDatabaseInfo = class
  public
    Path: string;
    Name: string;
    DBType: DBConnection.TDBType;
    constructor Create(const APath, AName: string; ADBType: DBConnection.TDBType);
  end;

  // 数据库管理器类
  TDatabaseManager = class
  private
    FConnection: TFDConnection;
    FTreeView: TTreeView;
    FDatabaseList: TObjectList<TDatabaseInfo>;
    FConfigFileName: string;
    FCurrentSelectedDB: string;
    FDatabasePathList: TStringList;
    FLastError: string;

    // 私有方法
    function GetIsConnected: Boolean;
    function FindOrCreateDirectoryNode(const DirPath: string): TTreeNode;
    function FindOrCreateDatabaseNode(DirNode: TTreeNode; const DBName, DBPath: string; DBType: DBConnection.TDBType): TTreeNode;
    function FindOrCreateExcelNodeInDirectory(DirNode: TTreeNode; const DirPath: string): TTreeNode;

  public
    constructor Create(AConnection: TFDConnection; ATreeView: TTreeView; const AConfigFileName: string);
    destructor Destroy; override;

    // 数据库连接方法
    function ConnectToDatabase(const APath: string; ADBType: DBConnection.TDBType): Boolean;
    procedure DisconnectFromDatabase;

    procedure LoadDatabaseStructure;

    // 数据库列表管理
    procedure SaveDatabaseList;
    procedure LoadDatabaseList;
    procedure BuildThreeLevelTreeStructure;
    procedure AddDatabaseToList(const APath, AName: string; ADBType: DBConnection.TDBType);
    procedure AddDatabaseToHistoryList(const APath, AName: string; ADBType: DBConnection.TDBType);
    procedure DeleteDatabaseFromHistory(const APath: string);
    procedure DeleteDirectoryFromHistory(const ADirPath: string);

    // 游戏配置相关
    function ReadGameConfig(const ARootPath: string): TStringList;

    function FindMirServerRoot(const APath: string): string;

    // 数据库操作
    procedure UpdateCurrentSelectedDatabase(const DBPath: string);

    // 属性
    property Connection: TFDConnection read FConnection;
    property IsConnected: Boolean read GetIsConnected;
    property DatabaseList: TObjectList<TDatabaseInfo> read FDatabaseList;
    property CurrentSelectedDB: string read FCurrentSelectedDB;
    property LastError: string read FLastError;
  end;

implementation

uses
  Winapi.Windows, UnifiedExcelProcessor_POI;

constructor TDatabaseInfo.Create(const APath, AName: string; ADBType: DBConnection.TDBType);
begin
  inherited Create;
  Path := APath;
  Name := AName;
  DBType := ADBType;
end;

constructor TDatabaseManager.Create(AConnection: TFDConnection; ATreeView: TTreeView; const AConfigFileName: string);
begin
  inherited Create;
  FConnection := AConnection;
  FTreeView := ATreeView;
  FConfigFileName := AConfigFileName;
  FDatabaseList := TObjectList<TDatabaseInfo>.Create(True);
  FDatabasePathList := TStringList.Create;
  FCurrentSelectedDB := '';
end;

destructor TDatabaseManager.Destroy;
begin
  if Assigned(FDatabaseList) then
    FDatabaseList.Free;
  if Assigned(FDatabasePathList) then
    FDatabasePathList.Free;
  inherited;
end;

function TDatabaseManager.GetIsConnected: Boolean;
begin
  Result := Assigned(FConnection) and FConnection.Connected;
end;

function TDatabaseManager.ConnectToDatabase(const APath: string; ADBType: DBConnection.TDBType): Boolean;
var
  DBManager: DBConnection.TDBConnectionManager;
begin
  FLastError := '';

  try
    // 使用DBConnection模块的统一连接管理
    DBManager := DBConnection.TDBConnectionManager.Create(FConnection, FTreeView);
    try
      // 委托给DBConnectionManager处理连接
      Result := DBManager.ConnectToDatabase(APath, ExtractFileName(APath), ADBType);

      if Result then
      begin
        // 更新当前选中的数据库
        UpdateCurrentSelectedDatabase(APath);

        // 加载数据库结构
        LoadDatabaseStructure;
      end
      else
      begin
        FLastError := '数据库连接失败: ' + APath;
      end;

    finally
      DBManager.Free;
    end;

  except
    on E: Exception do
    begin
      Result := False;
      FLastError := '数据库连接失败: ' + E.Message + ' (路径: ' + APath + ')';
    end;
  end;
end;

procedure TDatabaseManager.DisconnectFromDatabase;
begin
  if FConnection.Connected then
    FConnection.Close;
  FCurrentSelectedDB := '';
end;

procedure TDatabaseManager.UpdateCurrentSelectedDatabase(const DBPath: string);
begin
  FCurrentSelectedDB := DBPath;

  // 添加到数据库路径列表
  if FDatabasePathList.IndexOf(DBPath) = -1 then
    FDatabasePathList.Add(DBPath);
end;

procedure TDatabaseManager.LoadDatabaseStructure;
var
  DBManager: DBConnection.TDBConnectionManager;
  DBPath, DBName: string;
  DBType: DBConnection.TDBType;
  DBInfo: TDatabaseInfo;
  DirNode, DBNode, ExcelNode, TableNode: TTreeNode;
  Tables: TStringList;
  i: Integer;
  DBDir: string;
  FileExt: string;
begin
  if not Assigned(FTreeView) then
    Exit;

  // Get database path and type
  // 如果是Excel模式，则使用ExcelPath
  if FConnection.Params.Values['ExcelMode'] = '1' then
    DBPath := FConnection.Params.Values['ExcelPath']
  else
  begin
    DBPath := FConnection.Params.Database;
    if DBPath = '' then
      DBPath := FConnection.Params.Values['DefaultDir'];
  end;

  if DBPath = '' then
    Exit;

  // Determine database type from connection
  if FConnection.Connected or (FConnection.Params.Values['ExcelMode'] = '1') then
  begin
    // For connected databases or Excel mode, get type from DBManager
    DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
    try
      DBType := DBManager.DBType;
    finally
      DBManager.Free;
    end;
  end
  else
    Exit;

  DBName := ExtractFileName(DBPath);
  DBDir := ExtractFileDir(DBPath);

  // Find or create corresponding nodes in three-level structure
  DirNode := FindOrCreateDirectoryNode(DBDir);
  DBNode := FindOrCreateDatabaseNode(DirNode, DBName, DBPath, DBType);

  // Clear old table nodes under this database node
  for i := DBNode.Count - 1 downto 0 do
    DBNode.Item[i].Delete;

  // 获取表列表
  Tables := nil;
  if DBType = dbtExcel then
  begin
    // 对于Excel文件，获取XLS工作表名称列表
    DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
    try
      if DBManager.ConnectToExcelFile(DBPath) then
      begin
        Tables := DBManager.GetExcelSheetNames;
      end;
    finally
      DBManager.Free;
    end;

    // 如果获取不到，则尝试使用FastXLS直接获取
    if not Assigned(Tables) or (Tables.Count = 0) then
    begin
      if Assigned(Tables) then
        Tables.Free;

      try
        // 使用UnifiedExcelProcessor获取真实的工作表名称
        Tables := TExcelAPI.GetSheetNames(DBPath);
        if Tables.Count = 0 then
          Tables.Add(ChangeFileExt(DBName, '')); // 使用文件名作为默认表名
      except
        on E: Exception do
        begin
          // 如果FastXLS读取失败，创建默认表名
          Tables := TStringList.Create;
          Tables.Add(ChangeFileExt(DBName, '')); // 使用文件名作为默认表名
        end;
      end;
    end;
  end
  else if FConnection.Connected then
  begin
    // For regular databases, get table names from DBManager
    DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
    try
      try
        Tables := DBManager.GetTableNames;
      except
        on E: Exception do
        begin
          // Ignore errors when getting table names to avoid program crash
          // Database file might be corrupted or not exist
        end;
      end;
    finally
      DBManager.Free;
    end;
  end;

  // Create table nodes (但是Excel文件不创建表节点)
  if Assigned(Tables) and (Tables.Count > 0) and (DBType <> dbtExcel) then
  begin
    Tables.Sort;
    for i := 0 to Tables.Count - 1 do
    begin
      TableNode := FTreeView.Items.AddChild(DBNode, Tables[i]);
      TableNode.ImageIndex := 2; // Table icon
      TableNode.SelectedIndex := 2;
      TableNode.Data := nil; // Table nodes don't store special data
    end;
  end;

  if Assigned(Tables) then
    Tables.Free;

  // Expand database node to show all tables
  DBNode.Expand(False);

  // Ensure directory node is also expanded
  if Assigned(DirNode) then
    DirNode.Expand(False);
end;

function TDatabaseManager.FindMirServerRoot(const APath: string): string;
var
  CurrentPath: string;
  ParentPath: string;
begin
  Result := '';
  CurrentPath := APath;

  // 向上查找包含Mir200目录的根目录
  while CurrentPath <> '' do
  begin
    if DirectoryExists(CurrentPath + PathDelim + 'Mir200') then
    begin
      Result := CurrentPath;
      Exit;
    end;

    ParentPath := ExtractFileDir(CurrentPath);
    if ParentPath = CurrentPath then
      Break; // 到达根目录
    CurrentPath := ParentPath;
  end;
end;

function TDatabaseManager.ReadGameConfig(const ARootPath: string): TStringList;
var
  ConfigFile: string;
begin
  Result := TStringList.Create;

  ConfigFile := ARootPath + PathDelim + 'Config.ini';
  if FileExists(ConfigFile) then
  begin
    try
      Result.LoadFromFile(ConfigFile);
    except
      // 忽略读取错误
    end;
  end;
end;

procedure TDatabaseManager.SaveDatabaseList;
var
  IniFile: TIniFile;
  i: Integer;
begin
  if not Assigned(FDatabaseList) then
    Exit;

  try
    IniFile := TIniFile.Create(FConfigFileName);
    try
      // 清空INI文件中的数据库列表
      IniFile.EraseSection('DatabaseList');

      // 写入数据库数量
      IniFile.WriteInteger('DatabaseList', 'Count', FDatabaseList.Count);

      // 写入每个数据库信息
      for i := 0 to FDatabaseList.Count - 1 do
      begin
        IniFile.WriteString('DatabaseList', Format('Path%d', [i]), FDatabaseList[i].Path);
        IniFile.WriteString('DatabaseList', Format('Name%d', [i]), FDatabaseList[i].Name);
        IniFile.WriteInteger('DatabaseList', Format('DBType%d', [i]), Ord(FDatabaseList[i].DBType));
      end;
    finally
      IniFile.Free;
    end;
  except
    on E: Exception do
    begin
      // 保存数据库列表时发生错误
      // 忽略错误，避免影响程序正常运行
    end;
  end;
end;

procedure TDatabaseManager.LoadDatabaseList;
var
  IniFile: TIniFile;
  Count, i, DBTypeValue: Integer;
  Path, Name: string;
  DBType: DBConnection.TDBType;
begin
  if not Assigned(FDatabaseList) then
    Exit;

  // 清空现有的数据库列表
  FDatabaseList.Clear;

  try
    IniFile := TIniFile.Create(FConfigFileName);
    try
      // 读取数据库数量
      Count := IniFile.ReadInteger('DatabaseList', 'Count', 0);

      // 读取每个数据库信息
      for i := 0 to Count - 1 do
      begin
        Path := IniFile.ReadString('DatabaseList', Format('Path%d', [i]), '');
        Name := IniFile.ReadString('DatabaseList', Format('Name%d', [i]), '');
        DBTypeValue := IniFile.ReadInteger('DatabaseList', Format('DBType%d', [i]), 0);
        DBType := DBConnection.TDBType(DBTypeValue);

        // 只有当路径和名称不为空且文件存在时才添加
        if (Path <> '') and (Name <> '') and FileExists(Path) then
        begin
          FDatabaseList.Add(TDatabaseInfo.Create(Path, Name, DBType));
        end;
      end;
    finally
      IniFile.Free;
    end;
  except
    on E: Exception do
    begin
      // 加载数据库列表失败
    end;
  end;

  // 构建三级树形结构
  BuildThreeLevelTreeStructure;
end;

procedure TDatabaseManager.BuildThreeLevelTreeStructure;
var
  i: Integer;
  DBInfo: TDatabaseInfo;
  DBDir: string;
  DirNode, DBNode, ExcelNode: TTreeNode;
begin
  if not Assigned(FTreeView) then
    Exit;

  // 清空TreeView并重新构建三级结构
  // 只有当树为空时才构建
  if FTreeView.Items.Count = 0 then
  begin

    // 遍历数据库列表
    for i := 0 to FDatabaseList.Count - 1 do
    begin
      DBInfo := FDatabaseList[i];
      DBDir := ExtractFileDir(DBInfo.Path);

      // 查找或创建目录节点
      DirNode := FindOrCreateDirectoryNode(DBDir);

      // 判断是否是Excel文件
      if DBInfo.DBType = dbtExcel then
      begin
        // Excel文件需要创建一个"Excel"分组节点
        ExcelNode := FindOrCreateExcelNodeInDirectory(DirNode, DBDir);

        // 在Excel分组下创建Excel文件节点
        DBNode := FTreeView.Items.AddChild(ExcelNode, DBInfo.Name);
        DBNode.ImageIndex := 3; // Excel图标
        DBNode.SelectedIndex := 3;
        DBNode.Data := Pointer(DBInfo); // 存储数据库信息
      end
      else
      begin
        // 非Excel文件直接在目录下创建节点
        FindOrCreateDatabaseNode(DirNode, DBInfo.Name, DBInfo.Path, DBInfo.DBType);
      end;
    end;

    // 展开所有目录节点
    for i := 0 to FTreeView.Items.Count - 1 do
    begin
      if FTreeView.Items[i].Level = 0 then
        FTreeView.Items[i].Expand(False);
    end;
  end;
end;

procedure TDatabaseManager.AddDatabaseToList(const APath, AName: string; ADBType: DBConnection.TDBType);
var
  DBInfo: TDatabaseInfo;
  i: Integer;
begin
  if not Assigned(FDatabaseList) then
    Exit;

  // 检查是否已存在相同路径的数据库
  for i := 0 to FDatabaseList.Count - 1 do
  begin
    if SameText(FDatabaseList[i].Path, APath) then
    begin
      // 更新现有记录
      FDatabaseList[i].Name := AName;
      FDatabaseList[i].DBType := ADBType;
      Exit;
    end;
  end;

  // 添加新的数据库信息
  DBInfo := TDatabaseInfo.Create(APath, AName, ADBType);
  FDatabaseList.Add(DBInfo);
end;

procedure TDatabaseManager.AddDatabaseToHistoryList(const APath, AName: string; ADBType: DBConnection.TDBType);
begin
  // 添加到列表
  AddDatabaseToList(APath, AName, ADBType);

  // 保存到配置文件
  SaveDatabaseList;

  // 重新构建TreeView结构
  BuildThreeLevelTreeStructure;
end;

procedure TDatabaseManager.DeleteDatabaseFromHistory(const APath: string);
var
  i: Integer;
begin
  if not Assigned(FDatabaseList) then
    Exit;

  // 从数据库列表中删除指定路径的数据库
  for i := FDatabaseList.Count - 1 downto 0 do
  begin
    if SameText(FDatabaseList[i].Path, APath) then
    begin
      FDatabaseList.Delete(i);
      Break;
    end;
  end;

  // 保存配置
  SaveDatabaseList;

  // 重新构建TreeView结构
  BuildThreeLevelTreeStructure;
end;

procedure TDatabaseManager.DeleteDirectoryFromHistory(const ADirPath: string);
var
  i: Integer;
  DBDir: string;
begin
  if not Assigned(FDatabaseList) then
    Exit;

  // 删除指定目录下的所有数据库记录
  for i := FDatabaseList.Count - 1 downto 0 do
  begin
    DBDir := ExtractFileDir(FDatabaseList[i].Path);
    if SameText(DBDir, ADirPath) then
    begin
      FDatabaseList.Delete(i);
    end;
  end;

  // 保存配置
  SaveDatabaseList;

  // 重新构建TreeView结构
  BuildThreeLevelTreeStructure;
end;

function TDatabaseManager.FindOrCreateDirectoryNode(const DirPath: string): TTreeNode;
var
  i: Integer;
begin
  Result := nil;

  if not Assigned(FTreeView) then
    Exit;

  // 查找现有的目录节点
  for i := 0 to FTreeView.Items.Count - 1 do
  begin
    if (FTreeView.Items[i].Level = 0) and
       SameText(FTreeView.Items[i].Text, DirPath) then
    begin
      Result := FTreeView.Items[i];
      Exit;
    end;
  end;

  // 如果没找到则创建新的目录节点
  Result := FTreeView.Items.Add(nil, DirPath);
  Result.ImageIndex := 0; // 目录图标
  Result.SelectedIndex := 0;
  Result.Data := nil; // 目录节点不存储数据
end;

function TDatabaseManager.FindOrCreateExcelNodeInDirectory(DirNode: TTreeNode; const DirPath: string): TTreeNode;
var
  i: Integer;
begin
  Result := nil;

  if not Assigned(DirNode) then
    Exit;

  // 在目录节点下查找Excel分组节点
  for i := 0 to DirNode.Count - 1 do
  begin
    if SameText(DirNode.Item[i].Text, 'Excel') then
    begin
      Result := DirNode.Item[i];
      Exit;
    end;
  end;

  // 如果没找到则创建新的Excel分组节点
  Result := FTreeView.Items.AddChild(DirNode, 'Excel');
  Result.ImageIndex := 1; // 使用数据库图标或其他合适的图标
  Result.SelectedIndex := 1;
  Result.Data := nil; // Excel分组节点不存储数据
end;

function TDatabaseManager.FindOrCreateDatabaseNode(DirNode: TTreeNode; const DBName, DBPath: string; DBType: DBConnection.TDBType): TTreeNode;
var
  i: Integer;
  DBInfo: TDatabaseInfo;
begin
  Result := nil;

  if not Assigned(DirNode) then
    Exit;

  // 查找现有的数据库节点
  for i := 0 to DirNode.Count - 1 do
  begin
    if SameText(DirNode.Item[i].Text, DBName) then
    begin
      Result := DirNode.Item[i];
      Exit;
    end;
  end;

  // 如果没找到则创建新的数据库节点
  Result := FTreeView.Items.AddChild(DirNode, DBName);
  Result.ImageIndex := 1; // 数据库图标
  Result.SelectedIndex := 1;

  // 查找对应的数据库信息对象
  DBInfo := nil;
  for i := 0 to FDatabaseList.Count - 1 do
  begin
    if SameText(FDatabaseList[i].Path, DBPath) then
    begin
      DBInfo := FDatabaseList[i];
      Break;
    end;
  end;

  if not Assigned(DBInfo) then
  begin
    DBInfo := TDatabaseInfo.Create(DBPath, DBName, DBType);
    FDatabaseList.Add(DBInfo);
  end;

  Result.Data := Pointer(DBInfo); // 存储数据库信息
end;

end.