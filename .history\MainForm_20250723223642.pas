﻿unit MainForm;

interface

uses
  Winapi.Windows, Winapi.Messages, Winapi.ShellAPI, System.SysUtils, System.Variants, System.Classes, System.Types,
  System.UITypes, Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ComCtrls, Vcl.ExtCtrls,
  Vcl.Menus, Vcl.StdCtrls, <PERSON>cl<PERSON>, Vcl.ToolWin, System.ImageList, Vcl.ImgList,
  Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error, FireDAC.UI.Intf,
  FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool, FireDAC.Stan.Async,
  FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs,
  FireDAC.VCLUI.Wait, FireDAC.Comp.Client, System.Actions, Vcl.ActnList, Vcl.DBGrids,
  FireDAC.Comp.DataSet, System.Generics.Collections, DBConnection, System.IniFiles,
  Vcl.Grids, System.Math, Vcl.DBCtrls, CustomDBGrid,
  DatabaseManager, TableDataManager, UIManager, FormulaEngine, UnifiedExcelProcessor_POI,
  DataExportForm, DataImportForm, DatabaseSearchForm, DatabaseConvertForm,
  ExceptionLogger, ExceptionLogForm;

type
  TfrmMain = class(TForm)
    pnlMain: TPanel;
    sbMain: TStatusBar;
    mmMain: TMainMenu;
    miFile: TMenuItem;
    miConnect: TMenuItem;
    miDisconnect: TMenuItem;
    N1: TMenuItem;
    miExcelBrowser: TMenuItem;
    N2: TMenuItem;
    miExit: TMenuItem;
    miEdit: TMenuItem;
    miHelp: TMenuItem;
    miAbout: TMenuItem;
    tbMain: TToolBar;
    ilMain: TImageList;
    pnlLeft: TPanel;
    tvDatabases: TTreeView;
    // 新的现代化UI控件（已移除无用的组件声明）
    splVertical: TSplitter;
    pcRight: TPageControl;
    tsData: TTabSheet;
    alMain: TActionList;
    actConnect: TAction;
    actDisconnect: TAction;
    actExit: TAction;
    btnConnect: TToolButton;
    btnDisconnect: TToolButton;
    dbgData: TDBGrid;
    dsData: TDataSource;
    pmTreeView: TPopupMenu;
    pmDBGrid: TPopupMenu;
    actExportData: TAction;
    actImportData: TAction;
    actRefreshData: TAction;
    actModifyData: TAction;
    actRefreshDB: TAction;
    actSearchDB: TAction;
    actConvertDB: TAction;
    actSaveData: TAction;
    actExcelBrowser: TAction;
    miExportData: TMenuItem;
    miImportData: TMenuItem;
    miRefreshData: TMenuItem;
    miModifyData: TMenuItem;
    miRefreshDB: TMenuItem;
    miSearchDB: TMenuItem;
    miConvertDB: TMenuItem;
    miSaveData: TMenuItem;
    btnExportData: TToolButton;
    btnImportData: TToolButton;
    btnRefreshData: TToolButton;
    btnModifyData: TToolButton;
    btnRefreshDB: TToolButton;
    btnSearchDB: TToolButton;
    btnConvertDB: TToolButton;
    BitBtn1: TBitBtn;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure actConnectExecute(Sender: TObject);
    procedure actDisconnectExecute(Sender: TObject);
    procedure actExitExecute(Sender: TObject);
    procedure tvDatabasesDblClick(Sender: TObject);
    // 新的现代化UI事件处理
    procedure actExportDataExecute(Sender: TObject);
    procedure actImportDataExecute(Sender: TObject);
    procedure actRefreshDataExecute(Sender: TObject);
    procedure actModifyDataExecute(Sender: TObject);
    procedure actRefreshDBExecute(Sender: TObject);
    procedure actSearchDBExecute(Sender: TObject);
    procedure actConvertDBExecute(Sender: TObject);
    procedure actConfigExecute(Sender: TObject);
    procedure actExcelBrowserExecute(Sender: TObject);

    procedure tvDatabasesMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
    procedure tvDatabasesClick(Sender: TObject);
    procedure dbgDataMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
    // 表格编辑相关事件处理方法已移动到相应模块
    procedure dbgDataKeyPress(Sender: TObject; var Key: Char);
    procedure dbgDataKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
    procedure dbgDataEditButtonClick(Sender: TObject);
    procedure miTestFormulaClick(Sender: TObject);
    procedure OnFormulaCalculate(Sender: TObject);
  private
    { Private declarations }
    FConnection: TFDConnection;
    FCurrentQuery: TFDQuery;
    FCurrentTable: string;
    FConfigFileName: string;
    FFormulaDBGrid: TFormulaDBGrid; // 自定义DBGrid实例
    FCurrentExcelFile: string; // 当前打开的Excel文件路径
    FIsExcelData: Boolean; // 标识当前数据是否来自Excel文件

    // 管理器实例
    FDatabaseManager: TDatabaseManager;
    FTableDataManager: TTableDataManager;
    FUIManager: TUIManager;
    FFormulaEngine: TFormulaEngine;
    FErrorIndicator: TErrorIndicator;
    // FConfigManager: CleanConfigForm.TUnifiedConfigManager;

    // 简化的私有方法
    procedure InitializeControls;
    procedure InitializeManagers;
    procedure CleanupManagers;
    procedure InitializeErrorIndicator;
    procedure OnErrorIndicatorClick(Sender: TObject);
    procedure LoadExcelDataDirectly(const AFilePath: string);
    procedure SaveExcelData;
    procedure SetupExcelEditingMode(MemTable: TFDMemTable);
    procedure ForceCleanupExcelResources;

    // 辅助方法声明已移除

    // 事件处理方法
    procedure actSaveDataExecute(Sender: TObject);

    // Unicode消息显示
    procedure ShowUnicodeMessage(const Msg: string);
  public
    { Public declarations }
  end;

var
  frmMain: TfrmMain;

implementation

{$R *.dfm}

uses
  LegendColorPalette, CleanConfigForm
  {$IFDEF MSWINDOWS}
  {$IFDEF BDE_AVAILABLE}
  , BDE, DBTables
  {$ENDIF}
  {$ENDIF};

procedure TfrmMain.ShowUnicodeMessage(const Msg: string);
var
  WideMsg, WideTitle: WideString;
begin
  // 强制使用Unicode版本的MessageBox
  WideMsg := Msg;
  WideTitle := '提示信息';
  MessageBoxW(Handle, PWideChar(WideMsg), PWideChar(WideTitle), MB_OK or MB_ICONINFORMATION);
end;

procedure TfrmMain.FormCreate(Sender: TObject);
begin
  // 设置字符编码和字体以支持中文显示
  {$IFDEF MSWINDOWS}
  SetThreadLocale(LOCALE_SYSTEM_DEFAULT);
  {$ENDIF}

  // 强制设置窗体字体以支持中文显示
  Font.Name := 'Microsoft YaHei';
  Font.Charset := GB2312_CHARSET;
  Font.Size := 9;

  // 设置配置文件名
  FConfigFileName := ChangeFileExt(Application.ExeName, '.ini');

  // 创建数据库连接对象
  FConnection := TFDConnection.Create(Self);
  FConnection.Params.DriverID := 'SQLite';

  // 创建查询对象
  FCurrentQuery := TFDQuery.Create(Self);

  // 动态创建自定义DBGrid来替换标准DBGrid
  FFormulaDBGrid := TFormulaDBGrid.Create(Self);
  FFormulaDBGrid.Parent := dbgData.Parent;
  FFormulaDBGrid.Align := dbgData.Align;
  FFormulaDBGrid.Left := dbgData.Left;
  FFormulaDBGrid.Top := dbgData.Top;
  FFormulaDBGrid.Width := dbgData.Width;
  FFormulaDBGrid.Height := dbgData.Height;

  // 设置自定义DBGrid的选项
  FFormulaDBGrid.Options := [dgTitles, dgIndicator, dgColumnResize, dgColLines,
    dgRowLines, dgTabs, dgConfirmDelete, dgCancelOnExit, dgTitleClick,
    dgTitleHotTrack];
  FFormulaDBGrid.ReadOnly := True;
  FFormulaDBGrid.AllowFormulaInput := True;

  // 设置事件处理
  FFormulaDBGrid.OnFormulaCalculate := OnFormulaCalculate;
  // FFormulaDBGrid.OnDrawColumnCell := dbgDataDrawColumnCell;
  // FFormulaDBGrid.OnDblClick := dbgDataDblClick;

  // 彻底断开原始DBGrid与数据源的连接
  dbgData.DataSource := nil;
  dbgData.Visible := False;
  dbgData.Enabled := False;

  // 显示并激活自定义DBGrid
  FFormulaDBGrid.Visible := True;
  FFormulaDBGrid.Enabled := True;
  FFormulaDBGrid.TabStop := True;
  FFormulaDBGrid.TabOrder := dbgData.TabOrder;

  // 初始化控件和管理器
  // 初始化控件状态
  actConnect.Enabled := True;
  actDisconnect.Enabled := False;
  InitializeManagers;
  InitializeErrorIndicator;

  // 暂时注释掉自动检测功能，避免启动时的访问违例
  // var CurrentDir := GetCurrentDir;
  // if Assigned(FDatabaseManager) and FDatabaseManager.AutoDetectDatabase(CurrentDir) then
  // begin
  //   // 自动连接成功，更新UI
  //   FDatabaseManager.LoadDatabaseStructure;
  //   if Assigned(FUIManager) then
  //     FUIManager.LoadConnectedDatabaseTables;
  // end;
end;

procedure TfrmMain.FormDestroy(Sender: TObject);
begin
  try
    // 保存数据库列表（在清理管理器之前）
    if Assigned(FDatabaseManager) then
    begin
      try
        FDatabaseManager.SaveDatabaseList;
      except
        // 忽略保存错误，避免程序退出时崩溃
      end;
    end;

    // 清理管理器
    CleanupManagers;

    // 关闭数据库连接
    if Assigned(FConnection) and FConnection.Connected then
    begin
      try
        FConnection.Close;
      except
        // 忽略关闭错误
      end;
    end;

    // 释放查询对象
    if Assigned(FCurrentQuery) then
    begin
      try
        FCurrentQuery.Free;
      except
        // 忽略释放错误
      end;
    end;

    // 释放连接对象
    if Assigned(FConnection) then
    begin
      try
        FConnection.Free;
      except
        // 忽略释放错误
      end;
    end;

  except
    // 忽略所有销毁过程中的错误，确保程序能正常退出
  end;
end;

procedure TfrmMain.InitializeControls;
begin
  try
    // 设置页面控件
    if Assigned(pcRight) and Assigned(tsData) then
      pcRight.ActivePage := tsData;

    // 初始化Action状态（添加安全检查）
    if Assigned(actDisconnect) then
      actDisconnect.Enabled := False;
    if Assigned(actExportData) then
      actExportData.Enabled := True;  // 导出功能总是可用
    if Assigned(actImportData) then
      actImportData.Enabled := True;  // 导入功能总是可用
    if Assigned(actRefreshData) then
      actRefreshData.Enabled := False;
    if Assigned(actModifyData) then
      actModifyData.Enabled := False;
    if Assigned(actRefreshDB) then
      actRefreshDB.Enabled := False;
    if Assigned(actSearchDB) then
      actSearchDB.Enabled := True;  // 搜索功能总是可用
    if Assigned(actConvertDB) then
      actConvertDB.Enabled := True;  // 转换功能总是可用
    if Assigned(actSaveData) then
    begin
      actSaveData.Enabled := False;
      // 动态绑定OnExecute事件，避免DFM文件问题
      actSaveData.OnExecute := actSaveDataExecute;
    end;

    // 初始化弹出菜单
    if not Assigned(pmTreeView) then
      pmTreeView := TPopupMenu.Create(Self);

    if not Assigned(pmDBGrid) then
      pmDBGrid := TPopupMenu.Create(Self);

    // 设置TreeView的弹出菜单和事件
    if Assigned(tvDatabases) then
    begin
      tvDatabases.PopupMenu := pmTreeView;
      tvDatabases.OnMouseDown := tvDatabasesMouseDown;
      tvDatabases.OnClick := tvDatabasesClick;
    end;

    // 设置自定义DBGrid的弹出菜单和事件
    if Assigned(FFormulaDBGrid) then
    begin
      FFormulaDBGrid.PopupMenu := pmDBGrid;
      FFormulaDBGrid.OnMouseDown := dbgDataMouseDown;
    end;


  except
    on E: Exception do
    begin
      // 如果初始化控件失败，记录错误
      LogAndShowError('MainForm', '初始化控件时发生错误', E, '程序启动');
    end;
  end;
end;

procedure TfrmMain.InitializeManagers;
begin
  try
    // 创建配置管理器
    // FConfigManager := CleanConfigForm.TUnifiedConfigManager.Create(ChangeFileExt(Application.ExeName, '_config.ini'));

    // 创建数据库管理器
    FDatabaseManager := TDatabaseManager.Create(FConnection, tvDatabases, FConfigFileName);

    // 创建表数据管理器
    FTableDataManager := TTableDataManager.Create(FConnection, dsData, FFormulaDBGrid);

    // 创建UI管理器（传递正确的参数）
    FUIManager := TUIManager.Create(tvDatabases, nil, ilMain, nil, sbMain, alMain,
      FConnection, FDatabaseManager.DatabaseList);

    // 创建公式引擎
    FFormulaEngine := TFormulaEngine.Create(FFormulaDBGrid);

    // 初始化UI（分步进行，便于调试）
    if Assigned(FUIManager) then
    begin
      FUIManager.LoadIcons;
      // 初始化UI控件
      actConnect.Enabled := True;
      actDisconnect.Enabled := False;
      FUIManager.CreateModernUI;
    end;

    // 加载数据库历史列表
    if Assigned(FDatabaseManager) then
      FDatabaseManager.LoadDatabaseList;

    if Assigned(FUIManager) then
      FUIManager.LoadModernDatabaseList;

  except
    on E: Exception do
    begin
      // 如果初始化失败，记录错误
      LogAndShowError('MainForm', '初始化管理器时发生错误', E, '程序启动');
    end;
  end;
end;

procedure TfrmMain.CleanupManagers;
begin
  if Assigned(FErrorIndicator) then
    FErrorIndicator.Free;
  if Assigned(FFormulaEngine) then
    FFormulaEngine.Free;

  if Assigned(FUIManager) then
    FUIManager.Free;

  if Assigned(FTableDataManager) then
    FTableDataManager.Free;

  if Assigned(FDatabaseManager) then
    FDatabaseManager.Free;

  // if Assigned(FConfigManager) then
  //   FConfigManager.Free;
end;

procedure TfrmMain.InitializeErrorIndicator;
begin
  try
    // 创建错误指示器
    FErrorIndicator := TErrorIndicator.Create(Self);
    FErrorIndicator.Parent := sbMain;
    FErrorIndicator.Align := alRight;
    FErrorIndicator.Width := 30;
    FErrorIndicator.OnIndicatorClick := OnErrorIndicatorClick;

    // 检查是否有未读错误
    FErrorIndicator.CheckForErrors;
  except
    on E: Exception do
    begin
      // 如果错误指示器初始化失败，记录到日志但不影响程序运行
      LogAndShowError('MainForm', '错误指示器初始化失败', E, '程序启动', False);
    end;
  end;
end;

procedure TfrmMain.OnErrorIndicatorClick(Sender: TObject);
begin
  try
    // 打开异常日志窗体
    TfrmExceptionLog.ShowLogDialog;

    // 停止闪烁
    if Assigned(FErrorIndicator) then
      FErrorIndicator.StopFlashing;
  except
    on E: Exception do
    begin
      LogAndShowError('MainForm', '打开异常日志窗体失败', E, '用户点击错误指示器', True);
    end;
  end;
end;

procedure TfrmMain.actConnectExecute(Sender: TObject);
var
  OpenDialog: TOpenDialog;
  DBPath: string;
  DBType: DBConnection.TDBType;
  ConnectSuccess: Boolean;
begin
  // 初始化变量
  DBType := dbtSQLite; // 默认值

  // 显示文件选择对话框
  OpenDialog := TOpenDialog.Create(Self);
  try
    OpenDialog.Title := 'Select Database File';
    OpenDialog.Filter := 'SQLite Database (*.db;*.sqlite;*.sqlite3)|*.db;*.sqlite;*.sqlite3|' +
                        'Access Database (*.mdb;*.accdb)|*.mdb;*.accdb|' +
                        'Excel Files (*.xls)|*.xls|' +
                        'All Files (*.*)|*.*';
    OpenDialog.FilterIndex := 1;
    OpenDialog.Options := [ofHideReadOnly, ofFileMustExist, ofEnableSizing];

    if OpenDialog.Execute then
    begin
      DBPath := OpenDialog.FileName;

      // Determine database type based on file extension
      var Ext := LowerCase(ExtractFileExt(DBPath));
      if (Ext = '.db') or (Ext = '.sqlite') or (Ext = '.sqlite3') then
        DBType := dbtSQLite
      else if (Ext = '.mdb') or (Ext = '.accdb') then
        DBType := dbtAccess
      else if (Ext = '.xls') then
        DBType := dbtExcel
      else
        DBType := dbtSQLite; // Default

      // Use DatabaseManager to connect to database
      ConnectSuccess := FDatabaseManager.ConnectToDatabase(DBPath, DBType);
    end
    else
      ConnectSuccess := False;
  finally
    OpenDialog.Free;
  end;

  if ConnectSuccess then
  begin
    // Connection successful, update UI status
    FUIManager.UpdateStatusBar('Connected to database: ' + DBPath);

    // Add to database history
    var DBName := ExtractFileName(DBPath);
    FDatabaseManager.AddDatabaseToHistoryList(DBPath, DBName, DBType);

    // Update Action states
    actConnect.Enabled := True;
    actDisconnect.Enabled := True;
    actConnect.Enabled := True;  // Keep connect button enabled for connecting other databases
    actDisconnect.Enabled := True;
    actRefreshDB.Enabled := True;
    actSearchDB.Enabled := True;
    actConvertDB.Enabled := True;
    actSaveData.Enabled := True;

    // Load database structure
    FDatabaseManager.LoadDatabaseStructure;
  end;
end;

procedure TfrmMain.actDisconnectExecute(Sender: TObject);
begin
  if Assigned(FDatabaseManager) then
    FDatabaseManager.DisconnectFromDatabase;
  if Assigned(FUIManager) then
    actConnect.Enabled := True;
    actDisconnect.Enabled := False;
end;



procedure TfrmMain.actExitExecute(Sender: TObject);
begin
  Close;
end;



procedure TfrmMain.tvDatabasesDblClick(Sender: TObject);
var
  Node: TTreeNode;
  DBInfo: DatabaseManager.TDatabaseInfo;
  TableName: string;
  DBNode: TTreeNode;
  DatabaseInfo: DatabaseManager.TDatabaseInfo;
begin
  Node := tvDatabases.Selected;
  if not Assigned(Node) then
    Exit;

  case Node.Level of
    0: // 一级节点：目录路径
    begin
      // 展开或折叠目录节点
      if Node.Expanded then
        Node.Collapse(False)
      else
        Node.Expand(False);
    end;

    1: // 二级节点：数据库名
    begin
      // 连接到数据库
      if Assigned(Node.Data) then
      begin
        DBInfo := DatabaseManager.TDatabaseInfo(Node.Data);
        if Assigned(FDatabaseManager) then
        begin
          if FDatabaseManager.ConnectToDatabase(DBInfo.Path, DBInfo.DBType) then
          begin
            if Assigned(FUIManager) then
              FUIManager.UpdateStatusBar('已连接到数据库: ' + DBInfo.Name);

            // 展开数据库节点显示表
            if not Node.Expanded then
              Node.Expand(False);
          end
          else
          begin
            LogAndShowError('MainForm', '无法连接到数据库: ' + DBInfo.Path, nil, '用户双击数据库节点');
          end;
        end;
      end;
    end;

    2: // 三级节点：数据库表 或 Excel文件
    begin
      TableName := Node.Text;
      FCurrentTable := TableName;

      // 检查是否是Excel文件（父节点是"Excel"）
      if Assigned(Node.Parent) and SameText(Node.Parent.Text, 'Excel') then
      begin
        // 这是Excel文件，直接读取
        if Assigned(Node.Data) then
        begin
          DBInfo := DatabaseManager.TDatabaseInfo(Node.Data);
          try
            // 在打开新Excel文件前强制清理资源
            ForceCleanupExcelResources;
            LogInfo('MainForm', '打开Excel文件: ' + ExtractFileName(DBInfo.Path), '用户双击Excel节点');
            LoadExcelDataDirectly(DBInfo.Path);
          except
            on E: Exception do
              LogAndShowError('MainForm', '打开Excel文件失败', E, '用户双击Excel节点');
          end;
        end
        else
        begin
          LogAndShowError('MainForm', 'Excel文件信息缺失', nil, '用户双击Excel节点', True);
        end;
      end
      else
      begin
        // 这是普通数据库表，需要先连接数据库
        // 首先确保数据库已连接
        if not FConnection.Connected then
        begin
          // 尝试从父节点或祖父节点获取数据库信息并连接
          DBNode := Node.Parent;
          DatabaseInfo := nil;

          // 检查直接父节点是否有数据库信息
          if Assigned(DBNode) and Assigned(DBNode.Data) then
          begin
            DatabaseInfo := DatabaseManager.TDatabaseInfo(DBNode.Data);
          end
          else if Assigned(DBNode) and Assigned(DBNode.Parent) and Assigned(DBNode.Parent.Data) then
          begin
            // 检查祖父节点（可能是Excel节点的情况）
            DatabaseInfo := DatabaseManager.TDatabaseInfo(DBNode.Parent.Data);
          end;

          if Assigned(DatabaseInfo) then
          begin
            if Assigned(FDatabaseManager) then
            begin
              if not FDatabaseManager.ConnectToDatabase(DatabaseInfo.Path, DatabaseInfo.DBType) then
              begin
                LogAndShowError('MainForm', '无法连接到数据库: ' + DatabaseInfo.Path, nil, '用户双击表节点查看数据', True);
                Exit;
              end;
            end;
          end
          else
          begin
            LogAndShowWarning('MainForm', '请先双击数据库节点进行连接，然后再查看表数据', '用户双击表节点但数据库信息不存在');
            Exit;
          end;
        end;

        // 使用TableDataManager加载表数据
        if Assigned(FTableDataManager) then
        begin
          FTableDataManager.LoadTableData(TableName);
          if Assigned(FUIManager) then
            FUIManager.UpdateStatusBar('已加载表数据: ' + TableName);
        end
        else
          LogAndShowError('MainForm', '表数据管理器未初始化', nil, '用户双击表节点查看数据', True);
      end;
    end;
  end;

  // 使用UIManager处理TreeView双击事件
  if Assigned(FUIManager) then
    FUIManager.HandleTreeViewDoubleClick(Node, FCurrentTable);
end;



procedure TfrmMain.actExportDataExecute(Sender: TObject);
var
  DataSet: TDataSet;
begin
  if FCurrentTable <> '' then
  begin
    if Assigned(FTableDataManager) then
    begin
      // 获取当前数据集
      if Assigned(FTableDataManager.CurrentMemTable) and FTableDataManager.CurrentMemTable.Active then
        DataSet := FTableDataManager.CurrentMemTable
      else if Assigned(dsData.DataSet) and dsData.DataSet.Active then
        DataSet := dsData.DataSet
      else
      begin
        LogAndShowWarning('MainForm', '当前没有可导出的数据', '用户尝试导出数据但无可用数据集');
        Exit;
      end;

      // 显示导出对话框
      TfrmDataExport.ShowExportDialog(DataSet, FCurrentTable);
    end
    else
      LogAndShowError('MainForm', '表数据管理器未初始化', nil, '用户尝试导出数据', True);
  end
  else
    LogAndShowWarning('MainForm', '请先选择一个表', '用户尝试导出数据但未选择表');
end;

procedure TfrmMain.actImportDataExecute(Sender: TObject);
var
  DataSet: TDataSet;
begin
  if FCurrentTable <> '' then
  begin
    if Assigned(FTableDataManager) then
    begin
      // 获取当前数据集
      if Assigned(dsData.DataSet) and dsData.DataSet.Active then
        DataSet := dsData.DataSet
      else
      begin
        LogAndShowWarning('MainForm', '当前没有可导入的目标数据集', '用户尝试导入数据但无可用目标数据集');
        Exit;
      end;

      // 显示导入对话框
      TfrmDataImport.ShowImportDialog(DataSet, FCurrentTable);

      // 导入后刷新数据
      if Assigned(FTableDataManager) then
        FTableDataManager.RefreshTableData;
    end
    else
      LogAndShowError('MainForm', '表数据管理器未初始化', nil, '用户尝试导入数据', True);
  end
  else
    LogAndShowWarning('MainForm', '请先选择一个表', '用户尝试导入数据但未选择表');
end;

procedure TfrmMain.actRefreshDataExecute(Sender: TObject);
begin
  if not Assigned(FTableDataManager) then
  begin
    LogAndShowError('MainForm', '数据管理器未初始化', nil, '用户尝试刷新数据', True);
    Exit;
  end;

  // 统一处理数据刷新 - Excel和数据库表使用相同的操作体验
  if FTableDataManager.IsExcelDataMode then
  begin
    // Excel数据模式
    FTableDataManager.RefreshExcelData;
    if Assigned(FUIManager) then
      FUIManager.UpdateStatusBar('Excel数据刷新操作已执行')
    else
      LogInfo('MainForm', 'Excel数据刷新操作已执行', '用户刷新Excel数据');
  end
  else if FCurrentTable <> '' then
  begin
    // 数据库表模式
    FTableDataManager.LoadTableData(FCurrentTable);
    if Assigned(FUIManager) then
      FUIManager.UpdateStatusBar('数据库表数据已刷新: ' + FCurrentTable)
    else
      LogInfo('MainForm', '数据库表数据已刷新', '用户刷新数据库表数据');
  end
  else
  begin
    LogAndShowWarning('MainForm', '请先选择一个数据库表或加载Excel文件', '用户尝试刷新但未选择数据源');
  end;
end;

procedure TfrmMain.actModifyDataExecute(Sender: TObject);
begin
  if FCurrentTable <> '' then
  begin
    if Assigned(FTableDataManager) then
      LogInfo('MainForm', '修改表数据功能正在开发中', '用户尝试修改表数据')
    else
      LogAndShowError('MainForm', '表数据管理器未初始化', nil, '用户尝试修改表数据', True);
  end
  else
    LogAndShowWarning('MainForm', '请先选择一个表', '用户尝试修改表数据但未选择表');
end;

procedure TfrmMain.actRefreshDBExecute(Sender: TObject);
begin
  if FConnection.Connected then
  begin
    if Assigned(FDatabaseManager) then
      FDatabaseManager.LoadDatabaseStructure
    else
      LogAndShowError('MainForm', '数据库管理器未初始化', nil, '用户尝试刷新数据库', True);
  end
  else
    LogAndShowWarning('MainForm', '请先连接到数据库', '用户尝试刷新数据库但未连接');
end;

procedure TfrmMain.actSearchDBExecute(Sender: TObject);
begin
  if not FConnection.Connected then
  begin
    LogAndShowWarning('MainForm', '请先连接到数据库', '用户尝试搜索数据库但未连接');
    Exit;
  end;

  // 显示数据库搜索对话框
  TfrmDatabaseSearch.ShowSearchDialog(FConnection, FCurrentTable);
end;

procedure TfrmMain.actConvertDBExecute(Sender: TObject);
begin
  if Assigned(FDatabaseManager) then
    TfrmDatabaseConvert.ShowConvertDialog(FDatabaseManager)
  else
    LogAndShowError('MainForm', '数据库管理器未初始化', nil, '用户尝试转换数据库', True);
end;



procedure TfrmMain.actConfigExecute(Sender: TObject);
var
  ConfigForm: TfrmCleanConfig;
begin
  // 打开配置窗口
  ConfigForm := TfrmCleanConfig.Create(Self, nil);
  try
    // 显示配置窗口
    if ConfigForm.ShowModal = mrOK then
    begin
      if Assigned(FUIManager) then
        FUIManager.UpdateStatusBar('配置已保存')
      else
        LogInfo('MainForm', '配置已保存', '用户保存配置');
    end;
  finally
    ConfigForm.Free;
  end;
end;

procedure TfrmMain.tvDatabasesClick(Sender: TObject);
var
  Node: TTreeNode;
  HitTest: THitTests;
  Point: TPoint;
begin
  // 获取鼠标点击位置
  Point := tvDatabases.ScreenToClient(Mouse.CursorPos);
  HitTest := tvDatabases.GetHitTestInfoAt(Point.X, Point.Y);

  // 如果点击的是+/-按钮区域，让TreeView自己处理，不要干预
  if htOnButton in HitTest then
    Exit;

  Node := tvDatabases.Selected;
  if not Assigned(Node) then
    Exit;

  // 只有当点击在节点文本或图标区域时，才执行我们的展开/折叠逻辑
  if (htOnItem in HitTest) or (htOnIcon in HitTest) or (htOnLabel in HitTest) then
  begin
    case Node.Level of
      0: // 一级节点：目录路径
      begin
        // 单击展开或折叠目录节点
        if Node.Expanded then
          Node.Collapse(False)
        else
          Node.Expand(False);
      end;

      1: // 二级节点：数据库名
      begin
        // 单击展开或折叠数据库节点
        if Node.Expanded then
          Node.Collapse(False)
        else
          Node.Expand(False);
      end;

      2: // 三级节点：数据库表
      begin
        // 表节点不需要展开/折叠，保持选中状态即可
        FCurrentTable := Node.Text;
        if Assigned(FUIManager) then
          FUIManager.UpdateStatusBar('选中表: ' + FCurrentTable);
      end;
    end;
  end;
end;

procedure TfrmMain.tvDatabasesMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
var
  Node: TTreeNode;
begin
  if Button = mbRight then
  begin
    Node := tvDatabases.GetNodeAt(X, Y);
    if Assigned(Node) then
    begin
      tvDatabases.Selected := Node;

      // 根据节点级别设置弹出菜单项的可见性
      case Node.Level of
        0: // 一级节点：目录路径
        begin
          miRefreshDB.Visible := False;
          miSearchDB.Visible := False;
          miConvertDB.Visible := False;
          miExportData.Visible := False;
          miImportData.Visible := False;
          miRefreshData.Visible := False;
          miModifyData.Visible := False;
          miSaveData.Visible := False;
        end;

        1: // 二级节点：数据库名
        begin
          miRefreshDB.Visible := True;
          miSearchDB.Visible := True;
          miConvertDB.Visible := True;
          miExportData.Visible := False;
          miImportData.Visible := False;
          miRefreshData.Visible := False;
          miModifyData.Visible := False;
          miSaveData.Visible := False;
        end;

        2: // 三级节点：数据库表
        begin
          miRefreshDB.Visible := False;
          miSearchDB.Visible := False;
          miConvertDB.Visible := False;
          miExportData.Visible := True;
          miImportData.Visible := True;
          miRefreshData.Visible := True;
          miModifyData.Visible := True;
          miSaveData.Visible := True;

          // 保存当前选中的表名
          FCurrentTable := Node.Text;
        end;
      end;

      // 显示弹出菜单
      pmTreeView.Popup(Mouse.CursorPos.X, Mouse.CursorPos.Y);
    end;
  end;
end;

procedure TfrmMain.dbgDataMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
begin
  if Button = mbRight then
  begin
    // 在鼠标位置显示数据网格弹出菜单
    pmDBGrid.Popup(Mouse.CursorPos.X, Mouse.CursorPos.Y);
  end;
end;



procedure TfrmMain.dbgDataKeyPress(Sender: TObject; var Key: Char);
begin
  // 完全禁用MainForm的KeyPress处理，让自定义编辑器处理
  // 不做任何处理，让字符正常传递
end;



// 新增：KeyDown事件处理
procedure TfrmMain.dbgDataKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
begin
  // F2键进入编辑模式
  if Key = VK_F2 then
  begin
    if Assigned(FCurrentQuery) and FCurrentQuery.Active then
    begin
      if FCurrentQuery.State = dsBrowse then
        FCurrentQuery.Edit;
    end;
  end;
end;

procedure TfrmMain.dbgDataEditButtonClick(Sender: TObject);
begin
  // 编辑按钮点击事件（如果需要）
end;





procedure TfrmMain.actSaveDataExecute(Sender: TObject);
begin
  if not Assigned(FTableDataManager) then
  begin
    LogAndShowError('MainForm', '数据管理器未初始化', nil, '用户尝试保存数据', True);
    Exit;
  end;

  // 统一使用TableDataManager处理数据保存
  if FTableDataManager.IsExcelDataMode then
  begin
    // Excel数据模式 - 与数据库操作体验一致
    SaveExcelData;
    if Assigned(FUIManager) then
      FUIManager.UpdateStatusBar('Excel数据保存操作已执行')
    else
      LogInfo('MainForm', 'Excel数据保存操作已执行', '用户保存Excel数据');
  end
  else if FCurrentTable <> '' then
  begin
    // 数据库表模式
    FTableDataManager.SaveCurrentRecord;
    if Assigned(FUIManager) then
      FUIManager.UpdateStatusBar('数据库数据已保存')
    else
      LogInfo('MainForm', '数据库数据已保存', '用户保存数据库数据');
  end
  else
  begin
    LogAndShowWarning('MainForm', '请先选择一个数据库表或加载Excel文件', '用户尝试保存但未选择数据源');
  end;
end;



// 公式计算事件处理
procedure TfrmMain.OnFormulaCalculate(Sender: TObject);
begin
  // 当用户编辑完成时，自动保存数据
  if Assigned(FTableDataManager) then
  begin
    try
      // 对于Excel数据，立即保存到内存表
      if FTableDataManager.IsExcelDataMode then
      begin
        // Excel数据已经在DoExit中处理了，这里只需要标记为已修改
        if Assigned(FUIManager) then
          FUIManager.UpdateStatusBar('Excel数据已修改 - 请按Ctrl+S保存到文件');
      end
      else
      begin
        // 数据库数据，确保提交到数据库
        FTableDataManager.SaveCurrentRecord;
        if Assigned(FUIManager) then
          FUIManager.UpdateStatusBar('数据已保存到数据库');
      end;
    except
      on E: Exception do
      begin
        if Assigned(FUIManager) then
          FUIManager.UpdateStatusBar('保存数据时出错: ' + E.Message)
        else
          LogAndShowError('MainForm', '保存数据时出错', E, '用户保存数据', True);
      end;
    end;
  end;
end;

// 菜单项点击事件
procedure TfrmMain.miTestFormulaClick(Sender: TObject);
begin
  LogInfo('MainForm', '公式测试功能已移动到FormulaEngine模块', '用户点击公式测试菜单');
end;





procedure TfrmMain.actExcelBrowserExecute(Sender: TObject);
var
  OpenDialog: TOpenDialog;
  ExcelPath: string;
  ExcelName: string;
begin
  // 在选择新文件前强制清理Excel相关资源
  ForceCleanupExcelResources;

  // 显示Excel文件选择对话框
  OpenDialog := TOpenDialog.Create(Self);
  try
    OpenDialog.Title := '选择Excel文件';
    OpenDialog.Filter := 'Excel Files (*.xls)|*.xls|All Files (*.*)|*.*';
    OpenDialog.FilterIndex := 1;
    OpenDialog.Options := [ofHideReadOnly, ofFileMustExist, ofEnableSizing];

    if OpenDialog.Execute then
    begin
      ExcelPath := OpenDialog.FileName;

      // 统一使用直接读取方式
      try
        LogInfo('MainForm', '选择Excel文件: ' + ExtractFileName(ExcelPath), '用户选择Excel文件');
        LoadExcelDataDirectly(ExcelPath);

        // 添加到数据库历史（用于下次快速访问）
        if Assigned(FDatabaseManager) then
        begin
          ExcelName := ExtractFileName(ExcelPath);
          FDatabaseManager.AddDatabaseToHistoryList(ExcelPath, ExcelName, dbtExcel);
        end;

      except
        on E: Exception do
          LogAndShowError('MainForm', '打开Excel文件失败', E, '用户选择Excel文件', True);
      end;
    end;
  finally
    OpenDialog.Free;
  end;
end;

procedure TfrmMain.LoadExcelDataDirectly(const AFilePath: string);
var
  Processor: UnifiedExcelProcessor_POI.TUnifiedExcelProcessor;
  MemTable: TFDMemTable;
  OldDataSet: TDataSet;
begin
  try
    // 在加载新文件前再次确保资源清理
    LogInfo('MainForm', '开始加载Excel文件前的资源清理', '加载Excel文件');

    // 清理之前的数据源
    if Assigned(dsData) and Assigned(dsData.DataSet) then
    begin
      OldDataSet := dsData.DataSet;
      dsData.DataSet := nil;
      if Assigned(OldDataSet) then
      begin
        try
          OldDataSet.Close;
          OldDataSet.Free;
        except
          // 忽略释放错误
        end;
      end;
    end;

    // 设置当前Excel文件路径和标识
    FCurrentExcelFile := AFilePath;
    FIsExcelData := True;

    // 检查文件是否存在
    if not FileExists(AFilePath) then
    begin
      LogAndShowError('MainForm', '文件不存在: ' + AFilePath, nil, '加载Excel文件', True);
      Exit;
    end;

    // 检查文件是否被Excel占用
    LogInfo('MainForm', '检查文件访问权限...', '加载Excel文件');
    var TestFile: TFileStream;
    try
      // 尝试以共享读取方式打开文件
      TestFile := TFileStream.Create(AFilePath, fmOpenRead or fmShareDenyNone);
      TestFile.Free;
      LogInfo('MainForm', '文件访问正常，可以读取', '加载Excel文件');
    except
      on E: EInOutError do
      begin
        if E.ErrorCode = 32 then
          LogAndShowWarning('MainForm', '文件被其他程序占用（可能是Excel），但会尝试读取', '加载Excel文件')
        else
          LogAndShowWarning('MainForm', '文件访问错误: ' + E.Message + ' (错误代码: ' + IntToStr(E.ErrorCode) + ')', '加载Excel文件');
        // 继续尝试，因为有时候共享读取仍然可以工作
      end;
      on E: Exception do
      begin
        LogAndShowWarning('MainForm', '文件访问异常: ' + E.ClassName + ': ' + E.Message, '加载Excel文件');
        // 继续尝试
      end;
    end;

    // 使用统一的Excel处理器
    Processor := UnifiedExcelProcessor_POI.TUnifiedExcelProcessor.Create;
    try
      LogInfo('MainForm', '开始读取Excel文件: ' + AFilePath, '加载Excel文件');

      // 加载Excel文件
      if Processor.LoadFromFile(AFilePath) then
      begin
        if Processor.GetSheetCount > 0 then
        begin
          if Assigned(FUIManager) then
            FUIManager.UpdateStatusBar('Excel文件读取成功，工作表数量: ' + IntToStr(Processor.GetSheetCount))
          else
            LogInfo('MainForm', 'Excel文件读取成功，工作表数量: ' + IntToStr(Processor.GetSheetCount), '加载Excel文件');

          // 转换第一个工作表为数据集
          try
            MemTable := Processor.SheetToDataSet(0);
            if Assigned(FUIManager) then
              FUIManager.UpdateStatusBar('数据集转换成功')
            else
              LogInfo('MainForm', '数据集转换成功', '加载Excel文件');
          except
            on E: Exception do
            begin
              LogAndShowError('MainForm', '数据集转换失败', E, '加载Excel文件', True);
              Exit;
            end;
          end;

          if Assigned(MemTable) and (MemTable.RecordCount > 0) then
          begin
            if Assigned(FUIManager) then
              FUIManager.UpdateStatusBar('成功读取Excel文件，记录数: ' + IntToStr(MemTable.RecordCount))
            else
              LogInfo('MainForm', '成功读取Excel文件，记录数: ' + IntToStr(MemTable.RecordCount), '加载Excel文件');

            // 安全地处理数据源 - 使用自定义DBGrid和dsData数据源
            if Assigned(FFormulaDBGrid) and Assigned(dsData) then
            begin
              // 保存当前数据集引用
              OldDataSet := dsData.DataSet;

              // 设置新数据集到数据源
              dsData.DataSet := MemTable;

              // 激活数据集
              MemTable.Active := True;

              // 设置合适的列宽 - 避免单列占满整页
              var i: Integer;
              for i := 0 to MemTable.FieldCount - 1 do
              begin
                if MemTable.Fields[i] is TStringField then
                  TStringField(MemTable.Fields[i]).DisplayWidth := 15  // 字符宽度
                else if MemTable.Fields[i] is TWideStringField then
                  TWideStringField(MemTable.Fields[i]).DisplayWidth := 15;
              end;

              // 将数据源连接到自定义DBGrid
              FFormulaDBGrid.DataSource := dsData;

              // 使用TableDataManager统一管理Excel数据 - 与数据库表格操作保持一致
              if Assigned(FTableDataManager) then
              begin
                FTableDataManager.LoadExcelData(MemTable, AFilePath);
                LogInfo('MainForm', 'Excel数据已通过TableDataManager加载，支持与数据库相同的编辑操作', '加载Excel文件');
              end
              else
              begin
                // 备用方案：直接设置编辑模式
                SetupExcelEditingMode(MemTable);
              end;

              // 释放旧数据集
              if Assigned(OldDataSet) and (OldDataSet <> MemTable) then
                OldDataSet.Free;
            end
            else
            begin
              LogAndShowError('MainForm', '自定义数据网格或数据源未初始化', nil, '加载Excel文件', True);
              if Assigned(MemTable) then
                MemTable.Free;
              Exit;
            end;

            LogInfo('MainForm', 'Excel数据加载完成！', '加载Excel文件');
          end
          else
          begin
            LogAndShowWarning('MainForm', 'Excel文件中没有有效数据', '加载Excel文件');
            // 清理空的MemTable
            if Assigned(MemTable) then
              MemTable.Free;
          end;
        end
        else
        begin
          LogAndShowWarning('MainForm', 'Excel文件中没有工作表', '加载Excel文件');
        end;
      end
      else
      begin
        LogAndShowError('MainForm', '无法打开Excel文件: ' + AFilePath, nil, '加载Excel文件', True);
      end;
    finally
      // 强制清理Excel处理器
      if Assigned(Processor) then
      begin
        try
          Processor.Free;
        except
          // 忽略释放时的异常
        end;
        Processor := nil;
      end;

      // 强制内存清理，减少内存碎片
      try
        {$IFDEF MSWINDOWS}
        // 在Windows下强制内存整理，释放工作集
        SetProcessWorkingSetSize(GetCurrentProcess, $FFFFFFFF, $FFFFFFFF);
        {$ENDIF}

        // 强制应用程序处理消息队列，确保所有延迟释放的资源被处理
        Application.ProcessMessages;

        // 短暂延迟，让系统有时间清理资源
        Sleep(10);
      except
        // 忽略内存清理时的异常
      end;
    end;
  except
    on E: EInOutError do
    begin
      LogAndShowError('MainForm', '文件读取错误: ' + E.Message + ' (错误代码: ' + IntToStr(E.ErrorCode) + ')', E, '加载Excel文件', True);
    end;
    on E: Exception do
    begin
      LogAndShowError('MainForm', '读取Excel文件失败', E, '加载Excel文件', True);
    end;
  end;
end;

procedure TfrmMain.ForceCleanupExcelResources;
begin
  try
    // 强制清理当前数据源
    if Assigned(dsData) and Assigned(dsData.DataSet) then
    begin
      try
        dsData.DataSet.Close;
      except
        // 忽略关闭时的异常
      end;
      dsData.DataSet := nil;
    end;

    // 清理Excel相关状态
    FCurrentExcelFile := '';
    FIsExcelData := False;

    // 强制内存清理
    {$IFDEF MSWINDOWS}
    try
      // 释放工作集，强制系统回收内存
      SetProcessWorkingSetSize(GetCurrentProcess, $FFFFFFFF, $FFFFFFFF);
    except
      // 忽略内存清理异常
    end;
    {$ENDIF}

    // 处理消息队列，确保所有延迟操作完成
    Application.ProcessMessages;

    // 短暂延迟，让系统有时间清理文件句柄和其他资源
    Sleep(50);

    LogInfo('MainForm', 'Excel资源强制清理完成', '资源管理');
  except
    on E: Exception do
      LogAndShowWarning('MainForm', '清理Excel资源时发生异常: ' + E.Message, '资源管理');
  end;
end;

procedure TfrmMain.SaveExcelData;
begin
  try
    // 使用TableDataManager统一处理Excel数据保存
    if Assigned(FTableDataManager) and FTableDataManager.IsExcelDataMode then
    begin
      // 如果有原始文件路径，保存到原文件
      if FCurrentExcelFile <> '' then
      begin
        FTableDataManager.SaveExcelData(FCurrentExcelFile);
        if Assigned(FUIManager) then
          FUIManager.UpdateStatusBar('Excel数据已保存到: ' + ExtractFileName(FCurrentExcelFile))
        else
          LogInfo('MainForm', 'Excel数据已保存到: ' + FCurrentExcelFile, '保存Excel数据');
      end
      else
      begin
        // 显示保存对话框选择新位置
        var SaveDialog := TSaveDialog.Create(Self);
        try
          SaveDialog.Title := '保存Excel文件';
          SaveDialog.DefaultExt := 'xls';
          SaveDialog.Filter := 'Excel文件 (*.xls)|*.xls|CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*';
          SaveDialog.FileName := 'exported_data.xls';

          if SaveDialog.Execute then
          begin
            FTableDataManager.SaveExcelData(SaveDialog.FileName);
            if Assigned(FUIManager) then
              FUIManager.UpdateStatusBar('Excel数据已保存到: ' + ExtractFileName(SaveDialog.FileName))
            else
              LogInfo('MainForm', 'Excel数据已保存到: ' + SaveDialog.FileName, '保存Excel数据');
          end;
        finally
          SaveDialog.Free;
        end;
      end;
    end
    else
    begin
      LogAndShowWarning('MainForm', '当前不是Excel数据模式或没有可保存的数据', '保存Excel数据');
    end;

  except
    on E: Exception do
      LogAndShowError('MainForm', '保存Excel数据失败', E, '保存Excel数据', True);
  end;
end;



// 设置Excel数据编辑模式 - 与数据库表格操作保持一致
procedure TfrmMain.SetupExcelEditingMode(MemTable: TFDMemTable);
begin
  if not Assigned(MemTable) or not Assigned(FFormulaDBGrid) then
    Exit;

  try
    // 1. 启用编辑功能
    FFormulaDBGrid.ReadOnly := False;
    FFormulaDBGrid.Options := FFormulaDBGrid.Options + [dgEditing, dgAlwaysShowEditor];

    // 2. 启用公式输入功能
    FFormulaDBGrid.AllowFormulaInput := True;

    // 3. 设置数据集为可编辑状态
    if MemTable.Active then
    begin
      // 确保MemTable支持编辑
      MemTable.CachedUpdates := True; // 启用缓存更新，支持撤销
      MemTable.IndexFieldNames := ''; // 清除索引，允许编辑
    end;

    // 4. 设置数据源属性
    if Assigned(dsData) then
    begin
      dsData.AutoEdit := True; // 自动进入编辑模式
    end;

    // 5. 启用保存功能
    if Assigned(actSaveData) then
    begin
      actSaveData.Enabled := True;
      actSaveData.Caption := '保存Excel数据';
      actSaveData.Hint := '保存当前Excel数据的修改';
    end;

    // 6. 设置状态栏信息
    if Assigned(FUIManager) then
    begin
      FUIManager.UpdateStatusBar('Excel数据已加载 - 可编辑模式 (文件: ' + ExtractFileName(FCurrentExcelFile) + ')');
    end;

    // 7. 刷新网格显示
    FFormulaDBGrid.Refresh;

    LogInfo('MainForm', 'Excel编辑模式已启用，可以像编辑数据库表格一样编辑Excel数据', '设置Excel编辑模式');

  except
    on E: Exception do
    begin
      LogAndShowError('MainForm', '设置Excel编辑模式失败', E, '设置Excel编辑模式', True);
    end;
  end;
end;



end.
