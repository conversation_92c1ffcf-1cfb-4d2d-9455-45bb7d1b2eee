unit EquipNeedRuleForm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ComCtrls, ExceptionLogger;

type
  TfrmEquipNeedRule = class(TForm)
    lvRules: TListView;
    btnOK: TButton;
    btnCancel: TButton;
    lblTitle: TLabel;
    procedure FormCreate(Sender: TObject);
    procedure lvRulesDblClick(Sender: TObject);
    procedure btnOKClick(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    FSelectedNeedCode: Integer;
    procedure LoadRules;
  public
    class function ShowRuleSelector(var NeedCode: Integer): Boolean;
    property SelectedNeedCode: Integer read FSelectedNeedCode;
  end;

implementation

{$R *.dfm}

procedure TfrmEquipNeedRule.FormCreate(Sender: TObject);
begin
  FSelectedNeedCode := -1;

  // 设置字体以支持中文显示
  Font.Name := 'Microsoft YaHei UI';
  Font.Charset := DEFAULT_CHARSET;
  Font.Size := 9;

  Caption := #$88C5#$5907#$9700#$6C42#$89C4#$5219#$9009#$62E9#$5668;  // '装备需求规则选择器' 的Unicode编码
  Position := poScreenCenter;
  BorderStyle := bsDialog;
  Width := 600;
  Height := 500;

  lblTitle.Font.Assign(Font);
  lblTitle.Caption := #$8BF7#$9009#$62E9#$88C5#$5907#$9700#$6C42#$89C4#$5219#$7C7B#$578B;  // '请选择装备需求规则类型' 的Unicode编码
  lblTitle.Font.Style := [fsBold];
  lblTitle.Font.Size := 10;

  lvRules.Font.Assign(Font);
  lvRules.ViewStyle := vsReport;
  lvRules.RowSelect := True;
  lvRules.GridLines := True;

  // 清除现有列并重新添加
  lvRules.Columns.Clear;
  lvRules.Columns.Add.Caption := #$4EE3#$7801;  // '代码' 的Unicode编码
  lvRules.Columns.Add.Caption := #$89C4#$5219#$63CF#$8FF0;  // '规则描述' 的Unicode编码
  lvRules.Columns[0].Width := 80;
  lvRules.Columns[1].Width := 480;
  
  LoadRules;
end;

procedure TfrmEquipNeedRule.LoadRules;
var
  Item: TListItem;
begin
  lvRules.Items.Clear;
  
  Item := lvRules.Items.Add;
  Item.Caption := '0';
  Item.SubItems.Add(#$65E0#$9700#$6C42#$9650#$5236#$FF08#$6240#$6709#$804C#$4E1A#$53EF#$7528#$FF09);  // '无需求限制（所有职业可用）' 的Unicode编码
  Item.Data := Pointer(0);

  Item := lvRules.Items.Add;
  Item.Caption := '1';
  Item.SubItems.Add(#$6218#$58EB#$804C#$4E1A#$4E13#$7528#$FF08#$9700#$8981#$653B#$51FB#$529B#$FF09);  // '战士职业专用（需要攻击力）' 的Unicode编码
  Item.Data := Pointer(1);

  Item := lvRules.Items.Add;
  Item.Caption := '2';
  Item.SubItems.Add(#$6CD5#$5E08#$804C#$4E1A#$4E13#$7528#$FF08#$9700#$8981#$9B54#$6CD5#$529B#$FF09);  // '法师职业专用（需要魔法力）' 的Unicode编码
  Item.Data := Pointer(2);

  Item := lvRules.Items.Add;
  Item.Caption := '3';
  Item.SubItems.Add(#$9053#$58EB#$804C#$4E1A#$4E13#$7528#$FF08#$9700#$8981#$9053#$672F#$FF09);  // '道士职业专用（需要道术）' 的Unicode编码
  Item.Data := Pointer(3);

  Item := lvRules.Items.Add;
  Item.Caption := '4';
  Item.SubItems.Add(#$6218#$58EB#$0026#$6CD5#$5E08#$5171#$7528#$FF08#$653B#$51FB#$6216#$9B54#$6CD5#$FF09);  // '战士&法师共用（攻击或魔法）' 的Unicode编码
  Item.Data := Pointer(4);

  Item := lvRules.Items.Add;
  Item.Caption := '5';
  Item.SubItems.Add(#$6218#$58EB#$0026#$9053#$58EB#$5171#$7528#$FF08#$653B#$51FB#$6216#$9053#$672F#$FF09);  // '战士&道士共用（攻击或道术）' 的Unicode编码
  Item.Data := Pointer(5);

  Item := lvRules.Items.Add;
  Item.Caption := '6';
  Item.SubItems.Add(#$6CD5#$5E08#$0026#$9053#$58EB#$5171#$7528#$FF08#$9B54#$6CD5#$6216#$9053#$672F#$FF09);  // '法师&道士共用（魔法或道术）' 的Unicode编码
  Item.Data := Pointer(6);

  Item := lvRules.Items.Add;
  Item.Caption := '7';
  Item.SubItems.Add(#$9700#$8981#$7B49#$7EA7#$9650#$5236);  // '需要等级限制' 的Unicode编码
  Item.Data := Pointer(7);

  Item := lvRules.Items.Add;
  Item.Caption := '8';
  Item.SubItems.Add(#$9700#$8981#$58F0#$671B#$9650#$5236);  // '需要声望限制' 的Unicode编码
  Item.Data := Pointer(8);

  Item := lvRules.Items.Add;
  Item.Caption := '10';
  Item.SubItems.Add(#$8F6C#$751F#$7B49#$7EA7#$9650#$5236#$FF08#$9700#$8981#$8F6C#$751F#$6B21#$6570#$FF09);  // '转生等级限制（需要转生次数）' 的Unicode编码
  Item.Data := Pointer(10);

  Item := lvRules.Items.Add;
  Item.Caption := '11';
  Item.SubItems.Add(#$6218#$58EB#$8F6C#$751F#$4E13#$7528#$FF08#$8F6C#$751F#$6218#$58EB#$804C#$4E1A#$FF09);  // '战士转生专用（转生战士职业）' 的Unicode编码
  Item.Data := Pointer(11);

  Item := lvRules.Items.Add;
  Item.Caption := '12';
  Item.SubItems.Add(#$6CD5#$5E08#$8F6C#$751F#$4E13#$7528#$FF08#$8F6C#$751F#$6CD5#$5E08#$804C#$4E1A#$FF09);  // '法师转生专用（转生法师职业）' 的Unicode编码
  Item.Data := Pointer(12);

  Item := lvRules.Items.Add;
  Item.Caption := '13';
  Item.SubItems.Add(#$9053#$58EB#$8F6C#$751F#$4E13#$7528#$FF08#$8F6C#$751F#$9053#$58EB#$804C#$4E1A#$FF09);  // '道士转生专用（转生道士职业）' 的Unicode编码
  Item.Data := Pointer(13);

  Item := lvRules.Items.Add;
  Item.Caption := '14';
  Item.SubItems.Add(#$5185#$529F#$7B49#$7EA7#$FF08#$9700#$8981#$5185#$529F#$4FEE#$70BC#$7B49#$7EA7#$FF09);  // '内功等级（需要内功修炼等级）' 的Unicode编码
  Item.Data := Pointer(14);

  Item := lvRules.Items.Add;
  Item.Caption := '15';
  Item.SubItems.Add(#$6218#$58EB#$5185#$529F#$4E13#$7528#$FF08#$6218#$58EB#$5185#$529F#$4FEE#$70BC#$FF09);  // '战士内功专用（战士内功修炼）' 的Unicode编码
  Item.Data := Pointer(15);

  Item := lvRules.Items.Add;
  Item.Caption := '16';
  Item.SubItems.Add(#$6CD5#$5E08#$5185#$529F#$4E13#$7528#$FF08#$6CD5#$5E08#$5185#$529F#$4FEE#$70BC#$FF09);  // '法师内功专用（法师内功修炼）' 的Unicode编码
  Item.Data := Pointer(16);

  Item := lvRules.Items.Add;
  Item.Caption := '17';
  Item.SubItems.Add(#$9053#$58EB#$5185#$529F#$4E13#$7528#$FF08#$9053#$58EB#$5185#$529F#$4FEE#$70BC#$FF09);  // '道士内功专用（道士内功修炼）' 的Unicode编码
  Item.Data := Pointer(17);

  Item := lvRules.Items.Add;
  Item.Caption := '40';
  Item.SubItems.Add(#$82F1#$96C4#$7B49#$7EA7#$9650#$5236#$FF08#$9700#$8981#$82F1#$96C4#$7B49#$7EA7#$FF09);  // '英雄等级限制（需要英雄等级）' 的Unicode编码
  Item.Data := Pointer(40);

  Item := lvRules.Items.Add;
  Item.Caption := '41';
  Item.SubItems.Add(#$82F1#$96C4#$6218#$58EB#$4E13#$7528#$FF08#$82F1#$96C4#$6218#$58EB#$804C#$4E1A#$FF09);  // '英雄战士专用（英雄战士职业）' 的Unicode编码
  Item.Data := Pointer(41);

  Item := lvRules.Items.Add;
  Item.Caption := '42';
  Item.SubItems.Add(#$82F1#$96C4#$6CD5#$5E08#$4E13#$7528#$FF08#$82F1#$96C4#$6CD5#$5E08#$804C#$4E1A#$FF09);  // '英雄法师专用（英雄法师职业）' 的Unicode编码
  Item.Data := Pointer(42);

  Item := lvRules.Items.Add;
  Item.Caption := '43';
  Item.SubItems.Add(#$82F1#$96C4#$9053#$58EB#$4E13#$7528#$FF08#$82F1#$96C4#$9053#$58EB#$804C#$4E1A#$FF09);  // '英雄道士专用（英雄道士职业）' 的Unicode编码
  Item.Data := Pointer(43);

  Item := lvRules.Items.Add;
  Item.Caption := '44';
  Item.SubItems.Add(#$82F1#$96C4#$6218#$58EB#$0026#$6CD5#$5E08#$FF08#$82F1#$96C4#$653B#$51FB#$6216#$9B54#$6CD5#$FF09);  // '英雄战士&法师（英雄攻击或魔法）' 的Unicode编码
  Item.Data := Pointer(44);

  Item := lvRules.Items.Add;
  Item.Caption := '45';
  Item.SubItems.Add(#$82F1#$96C4#$6218#$58EB#$0026#$9053#$58EB#$FF08#$82F1#$96C4#$653B#$51FB#$6216#$9053#$672F#$FF09);  // '英雄战士&道士（英雄攻击或道术）' 的Unicode编码
  Item.Data := Pointer(45);

  Item := lvRules.Items.Add;
  Item.Caption := '46';
  Item.SubItems.Add(#$82F1#$96C4#$6CD5#$5E08#$0026#$9053#$58EB#$FF08#$82F1#$96C4#$9B54#$6CD5#$6216#$9053#$672F#$FF09);  // '英雄法师&道士（英雄魔法或道术）' 的Unicode编码
  Item.Data := Pointer(46);

  Item := lvRules.Items.Add;
  Item.Caption := '47';
  Item.SubItems.Add(#$82F1#$96C4#$8F6C#$751F#$9650#$5236#$FF08#$82F1#$96C4#$8F6C#$751F#$7B49#$7EA7#$FF09);  // '英雄转生限制（英雄转生等级）' 的Unicode编码
  Item.Data := Pointer(47);
  
  Item := lvRules.Items.Add;
  Item.Caption := '50';
  Item.SubItems.Add(#$7B49#$7EA7#$0026#$653B#$51FB);  // '等级&攻击' 的Unicode编码
  Item.Data := Pointer(50);

  Item := lvRules.Items.Add;
  Item.Caption := '51';
  Item.SubItems.Add(#$7B49#$7EA7#$0026#$9B54#$6CD5);  // '等级&魔法' 的Unicode编码
  Item.Data := Pointer(51);

  Item := lvRules.Items.Add;
  Item.Caption := '52';
  Item.SubItems.Add(#$7B49#$7EA7#$0026#$9053#$672F);  // '等级&道术' 的Unicode编码
  Item.Data := Pointer(52);

  Item := lvRules.Items.Add;
  Item.Caption := '53';
  Item.SubItems.Add(#$8F6C#$751F#$7B49#$7EA7#$0026#$653B#$51FB);  // '转生等级&攻击' 的Unicode编码
  Item.Data := Pointer(53);

  Item := lvRules.Items.Add;
  Item.Caption := '54';
  Item.SubItems.Add(#$8F6C#$751F#$7B49#$7EA7#$0026#$9B54#$6CD5);  // '转生等级&魔法' 的Unicode编码
  Item.Data := Pointer(54);

  Item := lvRules.Items.Add;
  Item.Caption := '55';
  Item.SubItems.Add(#$8F6C#$751F#$7B49#$7EA7#$0026#$9053#$672F);  // '转生等级&道术' 的Unicode编码
  Item.Data := Pointer(55);

  Item := lvRules.Items.Add;
  Item.Caption := '56';
  Item.SubItems.Add(#$5185#$529F#$7B49#$7EA7#$0026#$804C#$4E1A);  // '内功等级&职业' 的Unicode编码
  Item.Data := Pointer(56);

  Item := lvRules.Items.Add;
  Item.Caption := '60';
  Item.SubItems.Add(#$4F1A#$5458#$7B49#$7EA7#$9650#$5236);  // '会员等级限制' 的Unicode编码
  Item.Data := Pointer(60);

  Item := lvRules.Items.Add;
  Item.Caption := '70';
  Item.SubItems.Add(#$6027#$522B#$9650#$5236#$8981#$6C42);  // '性别限制要求' 的Unicode编码
  Item.Data := Pointer(70);

  Item := lvRules.Items.Add;
  Item.Caption := '81';
  Item.SubItems.Add(#$4E3B#$4F53#$7B49#$7EA7#$0026#$82F1#$96C4#$7B49#$7EA7#$53CC#$91CD#$9650#$5236);  // '主体等级&英雄等级双重限制' 的Unicode编码
  Item.Data := Pointer(81);

  Item := lvRules.Items.Add;
  Item.Caption := '82';
  Item.SubItems.Add(#$4E3B#$4F53#$804C#$4E1A#$0026#$82F1#$96C4#$804C#$4E1A#$53CC#$91CD#$804C#$4E1A#$9650#$5236);  // '主体职业&英雄职业双重职业限制' 的Unicode编码
  Item.Data := Pointer(82);
end;

procedure TfrmEquipNeedRule.FormShow(Sender: TObject);
begin
  // 确保字体和文本设置正确
  Font.Name := 'Microsoft YaHei UI';
  Font.Charset := DEFAULT_CHARSET;
  Font.Size := 9;

  Caption := '装备需求规则选择器';
  lblTitle.Caption := '请选择装备需求规则类型';
  btnOK.Caption := '确定';
  btnCancel.Caption := '取消';

  // 设置列标题
  if lvRules.Columns.Count >= 2 then
  begin
    lvRules.Columns[0].Caption := '代码';
    lvRules.Columns[1].Caption := '规则描述';
  end;

  if lvRules.Items.Count > 0 then
    lvRules.Items[0].Selected := True;
end;

procedure TfrmEquipNeedRule.lvRulesDblClick(Sender: TObject);
begin
  if Assigned(lvRules.Selected) then
  begin
    FSelectedNeedCode := Integer(lvRules.Selected.Data);
    ModalResult := mrOK;
  end;
end;

procedure TfrmEquipNeedRule.btnOKClick(Sender: TObject);
begin
  if Assigned(lvRules.Selected) then
  begin
    FSelectedNeedCode := Integer(lvRules.Selected.Data);
    ModalResult := mrOK;
  end
  else
  begin
    LogAndShowWarning('EquipNeedRuleForm', '请选择一个规则', '用户尝试确认但未选择规则');
  end;
end;

class function TfrmEquipNeedRule.ShowRuleSelector(var NeedCode: Integer): Boolean;
var
  Form: TfrmEquipNeedRule;
begin
  Result := False;
  Form := TfrmEquipNeedRule.Create(nil);
  try
    if Form.ShowModal = mrOK then
    begin
      NeedCode := Form.SelectedNeedCode;
      Result := True;
    end;
  finally
    Form.Free;
  end;
end;

end.
