unit CleanConfigForm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls, Vcl.ComCtrls, Vcl.Samples.Spin,
  System.IniFiles, ExceptionLogger;

type
  // 主题类型
  TThemeType = (ttClassic, ttModern, ttDark, ttLight, ttGaming);

  // 统一配置管理器
  TUnifiedConfigManager = class
  private
    FConfigFile: string;

    // 基本设置
    FFontName: string;
    FFontSize: Integer;
    FShowGrid: Boolean;
    FShowHeaders: Boolean;
    FEnableFormulas: Boolean;

    // 颜色设置
    FGridBackgroundColor: TColor;
    FSelectionColor: TColor;
    FTextColor: TColor;
    FEnableAnimations: Boolean;
    FTransparency: Integer;
    FRowHeight: Integer;
    FColumnWidth: Integer;
    FShowVerticalLines: Boolean;
    FShowHorizontalLines: Boolean;

    // 按钮属性
    FButtonWidth: Integer;
    FButtonHeight: Integer;
    FButtonMargin: Integer;
    FButtonBackgroundColor: TColor;
    FButtonBorderColor: TColor;
    FButtonTextColor: TColor;

    procedure LoadFromFile;
    procedure SaveToFile;
    procedure InitializeDefaults;

  public
    constructor Create(const ConfigFileName: string);
    destructor Destroy; override;

    // 基本属性
    property FontName: string read FFontName write FFontName;
    property FontSize: Integer read FFontSize write FFontSize;
    property ShowGrid: Boolean read FShowGrid write FShowGrid;
    property ShowHeaders: Boolean read FShowHeaders write FShowHeaders;
    property EnableFormulas: Boolean read FEnableFormulas write FEnableFormulas;

    // 颜色属性
    property GridBackgroundColor: TColor read FGridBackgroundColor write FGridBackgroundColor;
    property SelectionColor: TColor read FSelectionColor write FSelectionColor;
    property TextColor: TColor read FTextColor write FTextColor;
    property EnableAnimations: Boolean read FEnableAnimations write FEnableAnimations;
    property Transparency: Integer read FTransparency write FTransparency;
    property RowHeight: Integer read FRowHeight write FRowHeight;
    property ColumnWidth: Integer read FColumnWidth write FColumnWidth;
    property ShowVerticalLines: Boolean read FShowVerticalLines write FShowVerticalLines;
    property ShowHorizontalLines: Boolean read FShowHorizontalLines write FShowHorizontalLines;

    // 按钮属性
    property ButtonWidth: Integer read FButtonWidth write FButtonWidth;
    property ButtonHeight: Integer read FButtonHeight write FButtonHeight;
    property ButtonMargin: Integer read FButtonMargin write FButtonMargin;
    property ButtonBackgroundColor: TColor read FButtonBackgroundColor write FButtonBackgroundColor;
    property ButtonBorderColor: TColor read FButtonBorderColor write FButtonBorderColor;
    property ButtonTextColor: TColor read FButtonTextColor write FButtonTextColor;

    // 配置管理方法
    procedure SaveConfiguration;
    procedure LoadConfiguration;
    procedure ResetToDefaults;
    procedure ApplyTheme(ThemeType: TThemeType);

    // 导入导出
    procedure ExportSettings(const FileName: string);
    procedure ImportSettings(const FileName: string);
  end;
  TfrmCleanConfig = class(TForm)
    pnlMain: TPanel;
    gbBasic: TGroupBox;
    lblFont: TLabel;
    cbFontName: TComboBox;
    lblSize: TLabel;
    seFontSize: TSpinEdit;
    chkGrid: TCheckBox;
    chkHeaders: TCheckBox;
    chkFormulas: TCheckBox;

    gbTheme: TGroupBox;
    lblTheme: TLabel;
    cbTheme: TComboBox;
    btnApplyTheme: TButton;
    lblPrimaryColor: TLabel;
    pnlPrimaryColor: TPanel;
    btnPrimaryColor: TButton;
    lblGridBg: TLabel;
    pnlGridBg: TPanel;
    btnGridBg: TButton;

    gbButton: TGroupBox;
    lblButtonWidth: TLabel;
    lblButtonBgColor: TLabel;
    lblButtonMargin: TLabel;
    lblButtonBorderColor: TLabel;
    seButtonWidth: TSpinEdit;
    seButtonMargin: TSpinEdit;
    pnlButtonBgColor: TPanel;
    btnButtonBgColor: TButton;
    pnlButtonBorderColor: TPanel;
    btnButtonBorderColor: TButton;

    gbAdvanced: TGroupBox;
    lblTransparency: TLabel;
    tbTransparency: TTrackBar;
    lblTransValue: TLabel;
    chkAnimations: TCheckBox;
    lblRowHeight: TLabel;
    seRowHeight: TSpinEdit;
    lblColumnWidth: TLabel;
    seColumnWidth: TSpinEdit;

    pnlButtons: TPanel;
    btnOK: TButton;
    btnCancel: TButton;
    btnApply: TButton;
    btnReset: TButton;

    ColorDialog: TColorDialog;

    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure btnOKClick(Sender: TObject);
    procedure btnCancelClick(Sender: TObject);
    procedure btnApplyClick(Sender: TObject);
    procedure btnResetClick(Sender: TObject);
    procedure btnApplyThemeClick(Sender: TObject);
    procedure btnPrimaryColorClick(Sender: TObject);
    procedure btnGridBgClick(Sender: TObject);
    procedure btnButtonBgColorClick(Sender: TObject);
    procedure btnButtonBorderColorClick(Sender: TObject);
    procedure SettingChanged(Sender: TObject);
    procedure tbTransparencyChange(Sender: TObject);

  private
    FConfigManager: TUnifiedConfigManager;
    FOnConfigChanged: TNotifyEvent;
    FUpdating: Boolean;
    FOwnsConfigManager: Boolean; // 是否拥有配置管理器的所有权

    procedure LoadCurrentSettings;
    procedure ApplySettings;
    procedure TriggerConfigChanged;
    procedure UpdateControlFonts;

  public
    constructor Create(AOwner: TComponent; ConfigManager: TUnifiedConfigManager); reintroduce;
    destructor Destroy; override;
    property OnConfigChanged: TNotifyEvent read FOnConfigChanged write FOnConfigChanged;
  end;

implementation

{$R *.dfm}

constructor TfrmCleanConfig.Create(AOwner: TComponent; ConfigManager: TUnifiedConfigManager);
begin
  inherited Create(AOwner);
  FConfigManager := ConfigManager;
  FUpdating := False;
  FOwnsConfigManager := False;

  // 如果没有传入配置管理器，则创建一个新的
  if not Assigned(FConfigManager) then
  begin
    FConfigManager := TUnifiedConfigManager.Create(ChangeFileExt(Application.ExeName, '_unified_config.ini'));
    FOwnsConfigManager := True; // 标记我们拥有这个配置管理器
  end;
end;

destructor TfrmCleanConfig.Destroy;
begin
  // 只有当我们拥有配置管理器时才释放它
  if FOwnsConfigManager and Assigned(FConfigManager) then
    FConfigManager.Free;

  inherited Destroy;
end;

procedure TfrmCleanConfig.FormCreate(Sender: TObject);
begin
  // 强制设置字体以支持中文显示
  Font.Name := 'Microsoft YaHei';
  Font.Charset := GB2312_CHARSET;
  Font.Size := 9;

  // 设置系统代码页支持中文
  SetThreadLocale(LOCALE_SYSTEM_DEFAULT);

  Caption := #37197#32622#35774#32622;  // 配置设置
  Position := poScreenCenter;
  BorderStyle := bsDialog;
  Width := 500;
  Height := 520;

  // Initialize font combo
  cbFontName.Items.Clear;
  cbFontName.Items.Add(#24494#36719#38597#40657);  // 微软雅黑
  cbFontName.Items.Add(#23435#20307);              // 宋体
  cbFontName.Items.Add(#40657#20307);              // 黑体
  cbFontName.Items.Add(#26999#20307);              // 楷体
  cbFontName.Items.Add(#20223#23435);              // 仿宋
  cbFontName.Items.Add('Microsoft YaHei');

  // Initialize theme combo
  cbTheme.Items.Clear;
  cbTheme.Items.Add(#32463#20856#20027#39064);      // 经典主题
  cbTheme.Items.Add(#29616#20195#20027#39064);      // 现代主题
  cbTheme.Items.Add(#28145#33394#20027#39064);      // 深色主题
  cbTheme.Items.Add(#27973#33394#20027#39064);      // 浅色主题
  cbTheme.Items.Add(#28216#25103#20027#39064);      // 游戏主题

  // Set ranges
  seFontSize.MinValue := 8;
  seFontSize.MaxValue := 24;
  seFontSize.Value := 9;

  seRowHeight.MinValue := 16;
  seRowHeight.MaxValue := 100;
  seRowHeight.Value := 24;

  seColumnWidth.MinValue := 5;
  seColumnWidth.MaxValue := 50;
  seColumnWidth.Value := 15;

  tbTransparency.Min := 0;
  tbTransparency.Max := 100;
  tbTransparency.Position := 0;

  // Set color panels
  pnlPrimaryColor.BevelOuter := bvLowered;
  pnlPrimaryColor.Caption := '';
  pnlGridBg.BevelOuter := bvLowered;
  pnlGridBg.Caption := '';

  // 强制更新所有控件字体
  UpdateControlFonts;
end;

procedure TfrmCleanConfig.UpdateControlFonts;
var
  I: Integer;
  Component: TComponent;
begin
  // 遍历所有控件并设置字体
  for I := 0 to ComponentCount - 1 do
  begin
    Component := Components[I];
    if Component is TControl then
    begin
      if Component is TLabel then
        TLabel(Component).Font.Name := 'Microsoft YaHei'
      else if Component is TButton then
        TButton(Component).Font.Name := 'Microsoft YaHei'
      else if Component is TCheckBox then
        TCheckBox(Component).Font.Name := 'Microsoft YaHei'
      else if Component is TGroupBox then
        TGroupBox(Component).Font.Name := 'Microsoft YaHei';
    end;
  end;
end;

procedure TfrmCleanConfig.FormShow(Sender: TObject);
begin
  LoadCurrentSettings;
end;

procedure TfrmCleanConfig.LoadCurrentSettings;
begin
  if not Assigned(FConfigManager) or FUpdating then Exit;

  FUpdating := True;
  try
    // Load basic settings
    cbFontName.Text := FConfigManager.FontName;
    seFontSize.Value := FConfigManager.FontSize;
    chkGrid.Checked := FConfigManager.ShowGrid;
    chkHeaders.Checked := FConfigManager.ShowHeaders;
    chkFormulas.Checked := FConfigManager.EnableFormulas;

    // Load theme settings
    cbTheme.ItemIndex := 0; // 默认选择第一个主题
    pnlPrimaryColor.Color := FConfigManager.SelectionColor;
    pnlGridBg.Color := FConfigManager.GridBackgroundColor;

    // Load button settings
    seButtonWidth.Value := FConfigManager.ButtonWidth;
    seButtonMargin.Value := FConfigManager.ButtonMargin;
    pnlButtonBgColor.Color := FConfigManager.ButtonBackgroundColor;
    pnlButtonBorderColor.Color := FConfigManager.ButtonBorderColor;

    // Load advanced settings
    tbTransparency.Position := FConfigManager.Transparency;
    lblTransValue.Caption := IntToStr(FConfigManager.Transparency) + '%';
    chkAnimations.Checked := FConfigManager.EnableAnimations;
    seRowHeight.Value := FConfigManager.RowHeight;
    seColumnWidth.Value := FConfigManager.ColumnWidth;

  finally
    FUpdating := False;
  end;
end;

procedure TfrmCleanConfig.ApplySettings;
begin
  if not Assigned(FConfigManager) or FUpdating then Exit;

  try
    // Apply basic settings
    FConfigManager.FontName := cbFontName.Text;
    FConfigManager.FontSize := seFontSize.Value;
    FConfigManager.ShowGrid := chkGrid.Checked;
    FConfigManager.ShowHeaders := chkHeaders.Checked;
    FConfigManager.EnableFormulas := chkFormulas.Checked;

    // Apply color settings
    FConfigManager.GridBackgroundColor := pnlGridBg.Color;

    // Apply button settings
    FConfigManager.ButtonWidth := seButtonWidth.Value;
    FConfigManager.ButtonMargin := seButtonMargin.Value;
    FConfigManager.ButtonBackgroundColor := pnlButtonBgColor.Color;
    FConfigManager.ButtonBorderColor := pnlButtonBorderColor.Color;

    // Apply advanced settings
    FConfigManager.Transparency := tbTransparency.Position;
    FConfigManager.EnableAnimations := chkAnimations.Checked;
    FConfigManager.RowHeight := seRowHeight.Value;
    FConfigManager.ColumnWidth := seColumnWidth.Value;

    // Save configuration
    FConfigManager.SaveConfiguration;

    // Trigger config changed event
    TriggerConfigChanged;

  except
    on E: Exception do
      LogAndShowError('CleanConfigForm', '加载设置失败', E, '加载配置设置', True);
  end;
end;

procedure TfrmCleanConfig.TriggerConfigChanged;
begin
  if Assigned(FOnConfigChanged) then
    FOnConfigChanged(Self);
end;

procedure TfrmCleanConfig.SettingChanged(Sender: TObject);
begin
  if not FUpdating then
    ApplySettings;
end;

procedure TfrmCleanConfig.tbTransparencyChange(Sender: TObject);
begin
  lblTransValue.Caption := IntToStr(tbTransparency.Position) + '%';
  if not FUpdating then
    ApplySettings;
end;

procedure TfrmCleanConfig.btnOKClick(Sender: TObject);
begin
  ApplySettings;
  ModalResult := mrOK;
end;

procedure TfrmCleanConfig.btnCancelClick(Sender: TObject);
begin
  ModalResult := mrCancel;
end;

procedure TfrmCleanConfig.btnApplyClick(Sender: TObject);
begin
  ApplySettings;
end;

procedure TfrmCleanConfig.btnResetClick(Sender: TObject);
begin
  LogAndShowWarning('CleanConfigForm', string('用户尝试重置所有设置为默认值'), string('重置配置设置'));
  if MessageDlg(string('确定要重置所有设置为默认值吗？'), mtConfirmation, [mbYes, mbNo], 0) = mrYes then
  begin
    try
      FConfigManager.ResetToDefaults;
      LoadCurrentSettings;
      TriggerConfigChanged;
      LogInfo('CleanConfigForm', string('设置已重置为默认值'), string('重置配置设置'));
    except
      on E: Exception do
        LogAndShowError('CleanConfigForm', '重置设置失败', E, '重置配置设置', True);
    end;
  end;
end;

procedure TfrmCleanConfig.btnApplyThemeClick(Sender: TObject);
begin
  if cbTheme.ItemIndex >= 0 then
  begin
    try
      FConfigManager.ApplyTheme(TThemeType(cbTheme.ItemIndex));
      LoadCurrentSettings;
      TriggerConfigChanged;
      LogInfo('CleanConfigForm', string('设置已应用'), string('应用主题设置'));
    except
      on E: Exception do
        LogAndShowError('CleanConfigForm', '应用设置失败', E, '应用主题设置', True);
    end;
  end;
end;

procedure TfrmCleanConfig.btnPrimaryColorClick(Sender: TObject);
begin
  ColorDialog.Color := pnlPrimaryColor.Color;
  if ColorDialog.Execute then
  begin
    pnlPrimaryColor.Color := ColorDialog.Color;
    ApplySettings;
  end;
end;

procedure TfrmCleanConfig.btnGridBgClick(Sender: TObject);
begin
  ColorDialog.Color := pnlGridBg.Color;
  if ColorDialog.Execute then
  begin
    pnlGridBg.Color := ColorDialog.Color;
    ApplySettings;
  end;
end;

procedure TfrmCleanConfig.btnButtonBgColorClick(Sender: TObject);
begin
  ColorDialog.Color := pnlButtonBgColor.Color;
  if ColorDialog.Execute then
  begin
    pnlButtonBgColor.Color := ColorDialog.Color;
    ApplySettings;
  end;
end;

procedure TfrmCleanConfig.btnButtonBorderColorClick(Sender: TObject);
begin
  ColorDialog.Color := pnlButtonBorderColor.Color;
  if ColorDialog.Execute then
  begin
    pnlButtonBorderColor.Color := ColorDialog.Color;
    ApplySettings;
  end;
end;

{ TUnifiedConfigManager }

constructor TUnifiedConfigManager.Create(const ConfigFileName: string);
begin
  inherited Create;
  FConfigFile := ConfigFileName;

  // 初始化默认值
  InitializeDefaults;

  // 加载配置
  LoadConfiguration;
end;

destructor TUnifiedConfigManager.Destroy;
begin
  // 保存配置
  SaveConfiguration;

  inherited Destroy;
end;

procedure TUnifiedConfigManager.InitializeDefaults;
begin
  // 设置基本默认值
  FFontName := 'Microsoft YaHei';
  FFontSize := 9;
  FShowGrid := True;
  FShowHeaders := True;
  FEnableFormulas := True;

  // 设置颜色默认值
  FGridBackgroundColor := clWindow;
  FSelectionColor := clHighlight;
  FTextColor := clWindowText;
  FEnableAnimations := True;
  FTransparency := 0;
  FRowHeight := 24;
  FColumnWidth := 15;
  FShowVerticalLines := True;
  FShowHorizontalLines := True;

  // 设置按钮默认值
  FButtonWidth := 25;
  FButtonHeight := 20;
  FButtonMargin := 2;
  FButtonBackgroundColor := RGB(245, 245, 245);
  FButtonBorderColor := RGB(100, 100, 100);
  FButtonTextColor := RGB(0, 0, 0);
end;

procedure TUnifiedConfigManager.LoadFromFile;
var
  IniFile: TIniFile;
begin
  if not FileExists(FConfigFile) then
    Exit;

  IniFile := TIniFile.Create(FConfigFile);
  try
    // 读取基本设置
    FFontName := IniFile.ReadString('Basic', 'FontName', FFontName);
    FFontSize := IniFile.ReadInteger('Basic', 'FontSize', FFontSize);
    FShowGrid := IniFile.ReadBool('Basic', 'ShowGrid', FShowGrid);
    FShowHeaders := IniFile.ReadBool('Basic', 'ShowHeaders', FShowHeaders);
    FEnableFormulas := IniFile.ReadBool('Basic', 'EnableFormulas', FEnableFormulas);

    // 读取扩展设置
    FGridBackgroundColor := TColor(IniFile.ReadInteger('Extended', 'GridBackgroundColor', Integer(FGridBackgroundColor)));
    FSelectionColor := TColor(IniFile.ReadInteger('Extended', 'SelectionColor', Integer(FSelectionColor)));
    FTextColor := TColor(IniFile.ReadInteger('Extended', 'TextColor', Integer(FTextColor)));
    FEnableAnimations := IniFile.ReadBool('Extended', 'EnableAnimations', FEnableAnimations);
    FTransparency := IniFile.ReadInteger('Extended', 'Transparency', FTransparency);
    FRowHeight := IniFile.ReadInteger('Extended', 'RowHeight', FRowHeight);
    FColumnWidth := IniFile.ReadInteger('Extended', 'ColumnWidth', FColumnWidth);
    FShowVerticalLines := IniFile.ReadBool('Extended', 'ShowVerticalLines', FShowVerticalLines);
    FShowHorizontalLines := IniFile.ReadBool('Extended', 'ShowHorizontalLines', FShowHorizontalLines);

    // 读取按钮设置
    FButtonWidth := IniFile.ReadInteger('Button', 'ButtonWidth', 25);
    FButtonHeight := IniFile.ReadInteger('Button', 'ButtonHeight', 20);
    FButtonMargin := IniFile.ReadInteger('Button', 'ButtonMargin', 2);
    FButtonBackgroundColor := TColor(IniFile.ReadInteger('Button', 'ButtonBackgroundColor', Integer(RGB(245, 245, 245))));
    FButtonBorderColor := TColor(IniFile.ReadInteger('Button', 'ButtonBorderColor', Integer(RGB(100, 100, 100))));
    FButtonTextColor := TColor(IniFile.ReadInteger('Button', 'ButtonTextColor', Integer(RGB(0, 0, 0))));

  finally
    IniFile.Free;
  end;
end;

procedure TUnifiedConfigManager.SaveToFile;
var
  IniFile: TIniFile;
begin
  IniFile := TIniFile.Create(FConfigFile);
  try
    // 保存基本设置
    IniFile.WriteString('Basic', 'FontName', FFontName);
    IniFile.WriteInteger('Basic', 'FontSize', FFontSize);
    IniFile.WriteBool('Basic', 'ShowGrid', FShowGrid);
    IniFile.WriteBool('Basic', 'ShowHeaders', FShowHeaders);
    IniFile.WriteBool('Basic', 'EnableFormulas', FEnableFormulas);

    // 保存扩展设置
    IniFile.WriteInteger('Extended', 'GridBackgroundColor', Integer(FGridBackgroundColor));
    IniFile.WriteInteger('Extended', 'SelectionColor', Integer(FSelectionColor));
    IniFile.WriteInteger('Extended', 'TextColor', Integer(FTextColor));
    IniFile.WriteBool('Extended', 'EnableAnimations', FEnableAnimations);
    IniFile.WriteInteger('Extended', 'Transparency', FTransparency);
    IniFile.WriteInteger('Extended', 'RowHeight', FRowHeight);
    IniFile.WriteInteger('Extended', 'ColumnWidth', FColumnWidth);
    IniFile.WriteBool('Extended', 'ShowVerticalLines', FShowVerticalLines);
    IniFile.WriteBool('Extended', 'ShowHorizontalLines', FShowHorizontalLines);

    // 保存按钮设置
    IniFile.WriteInteger('Button', 'ButtonWidth', FButtonWidth);
    IniFile.WriteInteger('Button', 'ButtonHeight', FButtonHeight);
    IniFile.WriteInteger('Button', 'ButtonMargin', FButtonMargin);
    IniFile.WriteInteger('Button', 'ButtonBackgroundColor', Integer(FButtonBackgroundColor));
    IniFile.WriteInteger('Button', 'ButtonBorderColor', Integer(FButtonBorderColor));
    IniFile.WriteInteger('Button', 'ButtonTextColor', Integer(FButtonTextColor));

  finally
    IniFile.Free;
  end;
end;

procedure TUnifiedConfigManager.LoadConfiguration;
begin
  LoadFromFile;
end;

procedure TUnifiedConfigManager.SaveConfiguration;
begin
  SaveToFile;
end;

procedure TUnifiedConfigManager.ResetToDefaults;
begin
  InitializeDefaults;
  SaveConfiguration;
end;

procedure TUnifiedConfigManager.ApplyTheme(ThemeType: TThemeType);
begin
  // 根据主题类型设置相应的颜色
  case ThemeType of
    ttClassic:
    begin
      FGridBackgroundColor := clWindow;
      FSelectionColor := clHighlight;
      FTextColor := clWindowText;
    end;
    ttModern:
    begin
      FGridBackgroundColor := TColor($F8F8F8);
      FSelectionColor := TColor($D70078);
      FTextColor := TColor($202020);
    end;
    ttDark:
    begin
      FGridBackgroundColor := TColor($2D2D2D);
      FSelectionColor := TColor($D70078);
      FTextColor := TColor($FFFFFF);
    end;
    ttLight:
    begin
      FGridBackgroundColor := TColor($FFFFFF);
      FSelectionColor := TColor($E6E6E6);
      FTextColor := TColor($404040);
    end;
    ttGaming:
    begin
      FGridBackgroundColor := TColor($1E141E);
      FSelectionColor := TColor($00D7FF);
      FTextColor := TColor($FFC8C8);
    end;
  end;

  SaveConfiguration;
end;

procedure TUnifiedConfigManager.ExportSettings(const FileName: string);
begin
  // 先保存当前配置
  SaveConfiguration;

  // 复制配置文件
  if FileExists(FConfigFile) then
    CopyFile(PChar(FConfigFile), PChar(FileName), False);
end;

procedure TUnifiedConfigManager.ImportSettings(const FileName: string);
begin
  if FileExists(FileName) then
  begin
    // 复制导入文件
    CopyFile(PChar(FileName), PChar(FConfigFile), False);

    // 重新加载配置
    LoadConfiguration;
  end;
end;

end.
